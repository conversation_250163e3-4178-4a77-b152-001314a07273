module bit_to_64bit_converter (
    input  wire        clk,
    input  wire        rst_n,
    input  wire        in_valid,    //输入数据有效
    input  wire        one_pac_done, // 一个喷吹包解析完成
    input  wire        last_bit_of_pac_count,//用来判断是奇数包还是偶数包
    input  wire [127:0] in_data,    //输入的数据
    output wire        in_ready,    //模块可接收数据
    output wire        out_valid,   //输出到fifo完成
    output wire [63:0] out_data,    //输出到fifo的数据
    output wire [9:0]  fifo_wr_en,  // 修改为10位，对应10个子板的fifo使能
    output wire [9:0]  out_addr,    
    input  wire [31:0] udp_pac_num,  // 喷吹包计数 
    input  wire        device_start_stop,    //设备启动停止信号
    output reg         next_data_req //请求下一个128位数据
);
    // 状态定义
    localparam IDLE = 3'b000;
    localparam RECEIVE = 3'b001;
    localparam WAIT_DATA = 3'b010;
    localparam OUTPUT = 3'b011;
    localparam DONE = 3'b100;

    localparam LINE_NUM = 7'd124;

    reg [2:0] current_state, next_state;
    reg [5:0] group_cnt;    // 计数64组
    reg [6:0] bit_cnt;      // 计数128位
    reg [127:0] data_reg;   // 数据寄存器
    reg [6:0] out_data_cnt; // 输出数据计数器
    reg in_valid_r;         // 用于检测上升沿
    wire in_valid_pos;      // in_valid上升沿
    reg  [8:0] wr_ptr;
    // BRAM实例化 - 双端口RAM
    reg [63:0] bram [124:0];
    reg wr_en;
    reg [3:0] fifo_cnt; // FIFO计数器
    reg fifo_wr; // 临时写使能信号
    reg one_pac_done_r;
    wire one_pac_done_pos;
    reg  one_pac_done_sig;
    reg  device_start_stop_r;
    wire device_start_stop_pos;
    reg [31:0] udp_pac_num_r;
    reg [2:0] mod5_result1, mod5_result2;
    reg [63:0] out_data_reg;
    reg [9:0] out_addr_reg;
    // 检测in_valid上升沿
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            in_valid_r <= 1'b0;
            one_pac_done_r <= 1'b0;
            device_start_stop_r <= 1'b0;
        end else begin
            one_pac_done_r <= one_pac_done;
            in_valid_r <= in_valid;
            device_start_stop_r <= device_start_stop;
        end
    end

    assign one_pac_done_pos = one_pac_done && !one_pac_done_r;
    assign in_valid_pos = in_valid && !in_valid_r;
    assign device_start_stop_pos = device_start_stop && !device_start_stop_r;

    always @(posedge clk or negedge rst_n) begin
        if (!rst_n)
            one_pac_done_sig <= 1'b0;
        else if (one_pac_done_pos)
            one_pac_done_sig <= 1'b1;
        else if (current_state == IDLE)
            one_pac_done_sig <= 1'b0;
    end
    // 状态机
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n)
            current_state <= IDLE;
        else
            current_state <= next_state;
    end

    // 状态转换逻辑
    always @(*) begin
        case (current_state)
            IDLE: next_state = in_valid_pos ? RECEIVE : IDLE;
            RECEIVE: begin
                if (bit_cnt == LINE_NUM) begin
                    if (group_cnt == 6'd63 || one_pac_done_sig)
                        next_state = OUTPUT;
                    else
                        next_state = WAIT_DATA;
                end else
                    next_state = RECEIVE;
            end
            WAIT_DATA: next_state = in_valid_pos ? RECEIVE : WAIT_DATA;
            OUTPUT: next_state = (out_data_cnt == LINE_NUM + 1) ? DONE : OUTPUT;
            DONE: next_state = in_valid_pos ? RECEIVE : IDLE;
            default: next_state = IDLE;
        endcase
    end

    // FIFO计数器逻辑
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n)
            fifo_cnt <= 4'd0;
        else if (current_state == DONE && one_pac_done_sig)begin
            fifo_cnt <= 4'd0;
        end else if (current_state == DONE) begin
            fifo_cnt <= fifo_cnt + 1'b1;
        end
    end

    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            udp_pac_num_r <= 32'd0;
        end else begin
            udp_pac_num_r <= udp_pac_num;
        end
    end

    // ---- functions: nibble_mod5 / add_mod5 ----
    function [2:0] nibble_mod5(input [3:0] n);
    begin
        case (n)
        4'd0, 4'd5,  4'd10, 4'd15: nibble_mod5 = 3'd0;
        4'd1, 4'd6,  4'd11:        nibble_mod5 = 3'd1;
        4'd2, 4'd7,  4'd12:        nibble_mod5 = 3'd2;
        4'd3, 4'd8,  4'd13:        nibble_mod5 = 3'd3;
        default:                   nibble_mod5 = 3'd4; // 4,9,14
        endcase
    end
    endfunction

    function [2:0] add_mod5(input [2:0] a, input [2:0] b);
    reg [3:0] s;
    begin
        s = a + b;                       // 0..8
        add_mod5 = (s >= 5) ? s - 5 : s; // 0..4
    end
    endfunction

// 第一拍：折叠到 s0..s3 并寄存
reg [2:0] s0_r, s1_r, s2_r, s3_r;
always @(posedge clk or negedge rst_n) begin
  if (!rst_n) begin
    s0_r <= 3'd0; s1_r <= 3'd0; s2_r <= 3'd0; s3_r <= 3'd0;
  end else begin
    s0_r <= add_mod5(nibble_mod5(udp_pac_num_r[ 3: 0]),
                     nibble_mod5(udp_pac_num_r[ 7: 4]));
    s1_r <= add_mod5(nibble_mod5(udp_pac_num_r[11: 8]),
                     nibble_mod5(udp_pac_num_r[15:12]));
    s2_r <= add_mod5(nibble_mod5(udp_pac_num_r[19:16]),
                     nibble_mod5(udp_pac_num_r[23:20]));
    s3_r <= add_mod5(nibble_mod5(udp_pac_num_r[27:24]),
                     nibble_mod5(udp_pac_num_r[31:28]));
  end
end

// 第二拍：完成 t0/t1 和最终 mod5_result1；第三拍保持 mod5_result2
always @(posedge clk or negedge rst_n) begin
  if (!rst_n) begin
    mod5_result1 <= 3'd0;
    mod5_result2 <= 3'd0;
  end else begin
    mod5_result1 <= add_mod5( add_mod5(s0_r, s1_r), add_mod5(s2_r, s3_r) );
    mod5_result2 <= mod5_result1;
  end
end

    // 数据处理逻辑
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            group_cnt <= 6'd0;
            bit_cnt <= 7'd0;
            wr_en <= 1'b0;
            out_data_cnt <= 7'd0;
            next_data_req <= 1'b0;
            fifo_wr <= 1'b0;
            data_reg <= 128'd0;
            wr_ptr <= 9'd0;
            out_addr_reg <= 10'd0;
            out_data_reg <= 64'd0;
        end else begin
            wr_en <= 1'b0;
            next_data_req <= 1'b0;
            fifo_wr <= 1'b0;
            if(device_start_stop_pos) begin //设备重新启动后从头开始填充ram，pulse也要从头开始计数，上位机发送的udp_pac_num也要从0开始
                wr_ptr <= 9'd0;
            end
            case (current_state)
                IDLE: begin
                    if (in_valid_pos) begin
                        data_reg <= in_data;
                        bit_cnt <= 7'd0;
                        wr_en <= 1'b1;
                        group_cnt <= 6'd0;
                        case (mod5_result2)
                            3'd0: wr_ptr <= 9'd500;
                            3'd1: wr_ptr <= 9'd0;
                            3'd2: wr_ptr <= 9'd125;
                            3'd3: wr_ptr <= 9'd250;
                            3'd4: wr_ptr <= 9'd375;
                            default: wr_ptr <= 9'd0;
                        endcase 
                    end
                end
                RECEIVE: begin
                    if ((bit_cnt == LINE_NUM -1) && (group_cnt == 6'd63 || one_pac_done_sig)) begin
                        fifo_wr <= 1'b1;
                        bit_cnt <= bit_cnt + 1'b1;
                        wr_en <= 1'b1;
                    end else if (bit_cnt == LINE_NUM) begin
                        bit_cnt <= 7'd0;
                        wr_en <= 1'b0;
                        if (group_cnt == 6'd63 || one_pac_done_sig) begin
                            fifo_wr <= 1'b1;
                        end else begin
                            next_data_req <= 1'b1;
                        end
                    end else begin
                        bit_cnt <= bit_cnt + 1'b1;
                        wr_en <= 1'b1;
                    end
                end
                WAIT_DATA: begin
                    if (in_valid_pos) begin
                        data_reg <= in_data;
                        group_cnt <= group_cnt + 1'b1;
                        wr_en <= 1'b1;//提前一个周期写使能
                    end else begin
                        next_data_req <= 1'b1;//等待的时候拉高，防止检测不到上升沿
                    end
                end
                OUTPUT: begin
                    if (out_data_cnt == LINE_NUM + 1) begin
                        out_data_cnt <= 7'd0;
                        fifo_wr <= 1'b0;
                    end else begin
                        out_data_cnt <= out_data_cnt + 1'b1;
                        out_data_reg <= bram[out_data_cnt];
                        out_addr_reg <= wr_ptr + out_data_cnt;
                        fifo_wr <= 1'b1;
                    end
                end
                DONE: begin
                    fifo_wr <= 1'b0;
                    // if (one_pac_done_sig) begin//收完一帧后，指向下一个ram地址，执行乒乓操作
                    //     case (mod5_result2)
                    //         3'd0: wr_ptr <= 9'd0;
                    //         3'd1: wr_ptr <= 9'd125;
                    //         3'd2: wr_ptr <= 9'd250;
                    //         3'd3: wr_ptr <= 9'd375;
                    //         3'd4: wr_ptr <= 9'd500;
                    //         default: wr_ptr <= 9'd0;
                    //     endcase 
                    // end
                end
                default: begin
                    wr_en <= 1'b0;
                end
            endcase
        end
    end

    reg [6:0] bit_cnt_dly,bit_cnt_dly1;
    reg [127:0] data_reg_dly,data_reg_dly1;
    reg [5:0] group_cnt_dly,group_cnt_dly1;
    reg wr_en_dly,wr_en_dly1;

    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            bit_cnt_dly <= 7'd0;
            data_reg_dly <= 128'd0;
            group_cnt_dly <= 6'd0;
            wr_en_dly <= 1'b0;
            bit_cnt_dly1 <= 7'd0;
            data_reg_dly1 <= 128'd0;
            group_cnt_dly1 <= 6'd0;
            wr_en_dly1 <= 1'b0;
        end else begin
            bit_cnt_dly1 <= bit_cnt_dly;
            data_reg_dly1 <= data_reg_dly;
            group_cnt_dly1 <= group_cnt_dly;
            wr_en_dly1 <= wr_en_dly;

            bit_cnt_dly <= bit_cnt;
            data_reg_dly <= data_reg;
            group_cnt_dly <= group_cnt;
            wr_en_dly <= wr_en;
        end
    end

    always @(posedge clk) begin
        if (wr_en_dly1) begin
            bram[bit_cnt_dly1][63-group_cnt_dly1] <= data_reg_dly1[7'd127-bit_cnt_dly1];
        end
    end
    // BRAM写入逻辑
    // always @(posedge clk) begin
    //     if (wr_en) begin
    //         bram[bit_cnt][63-group_cnt] <= data_reg[7'd127-bit_cnt];
    //     end
    // end

    // FIFO写使能解码逻辑
    assign fifo_wr_en = fifo_wr ? (10'b1 << fifo_cnt) : 10'b0;

    // 输出逻辑
    assign in_ready = (current_state == IDLE);
    assign out_valid = (current_state == DONE);
    assign out_data = out_data_reg;
    assign out_addr = out_addr_reg;

endmodule
