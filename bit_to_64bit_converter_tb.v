`timescale 1ns/1ps

module bit_to_64bit_converter_tb();
    reg clk;
    reg rst_n;
    reg in_valid;
    reg one_pac_done;
    reg last_bit_of_pac_count;
    reg [127:0] in_data;
    wire in_ready;
    wire out_valid;
    wire [63:0] out_data;
    wire [9:0] fifo_wr_en;
    wire [9:0] out_addr;
    reg [31:0] udp_pac_num;  // 喷吹包计数 
    reg  device_start_stop;    //设备启动停止信号
    wire next_data_req;

    // 实例化被测试模块
    bit_to_64bit_converter u_converter(
        .clk(clk),
        .rst_n(rst_n),
        .in_valid(in_valid),
        .one_pac_done(one_pac_done),
        .last_bit_of_pac_count(last_bit_of_pac_count),
        .in_data(in_data),
        .in_ready(in_ready),
        .out_valid(out_valid),
        .out_data(out_data),
        .fifo_wr_en(fifo_wr_en),
        .out_addr(out_addr),
        .device_start_stop(device_start_stop),
        .udp_pac_num(udp_pac_num),
        .next_data_req(next_data_req)
    );

    // 时钟生成
    always begin
        #5 clk = ~clk;
    end

    // 测试激励
    initial begin
        // 初始化信号
        clk = 0;
        rst_n = 1;
        in_valid = 0;
        in_data = 0;
        one_pac_done = 0;
        last_bit_of_pac_count = 1'b0;
        udp_pac_num = 32'd1;
        // 复位操作
        #20 rst_n = 0;
        #20 rst_n = 1;

        // 等待系统稳定
        #20;

        // 连续发送64组数据
        repeat(80) begin
            @(posedge clk);
            in_valid = 1;
            // 每次发送不同的测试数据
            // in_data = {$random, $random, $random, $random};
            in_data = {32'h11_22_33_44, 32'h11_22_33_44, 32'h11_22_33_44, 32'h11_22_33_44};
            // 等待next_data_req信号
            @(posedge next_data_req or posedge out_valid);
            #10 in_valid = 0;
            
            // 等待一个时钟周期再发送下一组
            @(posedge clk);
        end

            in_valid = 1;
            in_data = {32'h11_22_33_44, 32'h11_22_33_44, 32'h11_22_33_44, 32'h11_22_33_44};
            #10 one_pac_done = 1;
            #10 one_pac_done = 0;
            #10 in_valid = 0;
            last_bit_of_pac_count = 1'b1;
        @(posedge out_valid);

        $display("second pac test===================================");
        udp_pac_num =32'd2;
        #80;
        repeat(80) begin
            @(posedge clk);
            in_valid = 1;
            // 每次发送不同的测试数据
            // in_data = {$random, $random, $random, $random};
            in_data = {32'h11_22_33_44, 32'h11_22_33_44, 32'h11_22_33_44, 32'h11_22_33_44};
            // 等待next_data_req信号
            @(posedge next_data_req or posedge out_valid);
            #10 in_valid = 0;
            
            // 等待一个时钟周期再发送下一组
            @(posedge clk);
        end

            in_valid = 1;
            in_data = {32'h11_22_33_44, 32'h11_22_33_44, 32'h11_22_33_44, 32'h11_22_33_44};
            #10 one_pac_done = 1;
            #10 one_pac_done = 0;
            #10 in_valid = 0;
            last_bit_of_pac_count = 1'b0;
        // 等待数据处理完成
        wait(out_valid);
        
        $display("third pac test===================================");
        udp_pac_num = 32'd3;
        #80;
        repeat(80) begin
            @(posedge clk);
            in_valid = 1;
            // 每次发送不同的测试数据
            // in_data = {$random, $random, $random, $random};
            in_data = {32'h11_22_33_44, 32'h11_22_33_44, 32'h11_22_33_44, 32'h11_22_33_44};
            // 等待next_data_req信号
            @(posedge next_data_req or posedge out_valid);
            #10 in_valid = 0;
            
            // 等待一个时钟周期再发送下一组
            @(posedge clk);
        end

            in_valid = 1;
            in_data = {32'h11_22_33_44, 32'h11_22_33_44, 32'h11_22_33_44, 32'h11_22_33_44};
            #10 one_pac_done = 1;
            #10 one_pac_done = 0;
            #10 in_valid = 0;
            last_bit_of_pac_count = 1'b0;
        // 等待数据处理完成
        wait(out_valid);

        // 验证输出
        if (out_valid) begin
            $display("Test completed - Output data valid");
            // 显示BRAM内容
            display_bram_contents();
            
            // 检查输出数据
            // repeat(128) begin
            //     @(posedge clk);
            //     $display("out_addr=%d, out_data=%h", out_addr, out_data);
            // end
        end

        #1300;
        $display("Simulation finished");
        $finish;
    end

    // 监控信号变化
    initial begin
        $monitor("Time=%0t rst_n=%b in_valid=%b out_valid=%b fifo_wr_en=%b out_addr=%d",
                 $time, rst_n, in_valid, out_valid, fifo_wr_en, out_addr);
    end

    // 添加BRAM内容监视任务
    task display_bram_contents;
        integer i, j;
        begin
            $display("\n=== BRAM Contents ===");
            for (i = 0; i < 125; i = i + 1) begin
                $display("BRAM[%3d] = %h", i, u_converter.bram[i]);
            end
            $display("==================\n");
        end
    endtask

    // 波形输出
    initial begin
        $dumpfile("bit_to_64bit_converter_tb.vcd");
        $dumpvars(0, bit_to_64bit_converter_tb);
    end

endmodule
