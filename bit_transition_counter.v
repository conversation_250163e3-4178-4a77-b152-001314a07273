module bit_transition_counter (
    input  wire        clk,           // 系统时钟 125MHz
    input  wire        rst_n,         // 低电平复位
    input  wire        input_valid,   // 输入数据有效信号
    input  wire        one_pac_done,   // 一个数据包传输完成信号
    input  wire [124:0] data_in,      // 输入数据
    input  wire         one_sec_sig,   // 
    // output reg  [7:0]  trans_count,   // 跳变计数结果（最多125个跳变）
    output reg  [31:0] valve_open_count, // 喷吹阀门开启计数
    // output reg         count_valid,   // 计数结果有效
    output reg  [31:0] valve_open_per_second   //  每秒的阀门开启次数
);

// input_valid 上升沿检测
reg  input_valid_r;
wire input_valid_pos;
reg one_pac_done_r;
wire one_pac_done_pos;

// 1秒计数器
// reg [26:0] one_second_counter;
localparam ONE_SECOND_COUNT = 125_000_000; // 125MHz时钟下，1秒对应的计数值
reg [31:0] last_valve_open_count;
reg [359:0] valve_cnt;
reg [8:0]   valve_bit_index;

assign input_valid_pos = input_valid & ~input_valid_r;
assign one_pac_done_pos = one_pac_done & ~one_pac_done_r;
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        input_valid_r <= 1'b0;
        one_pac_done_r <= 1'b0;
    end
    else begin
        input_valid_r <= input_valid;
        one_pac_done_r <= one_pac_done;
    end
end

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        valve_bit_index <= 9'd0;
        valve_cnt <= 360'd0;
    end
    else begin
        if (input_valid_pos) begin
            valve_cnt[valve_bit_index] <= data_in[0];  // 保存当前数据的最后一位
            valve_bit_index <= valve_bit_index + 1'b1;
        end
        if(one_pac_done_pos)begin
            valve_bit_index <= 9'd0;
        end
    end
end

// 使用组合逻辑计算相邻位之间的跳变，包括与前一数据包的连接处
// wire [124:0] transitions;
// assign transitions = {(~last_bit & data_in[124]) , (~data_in[124:1] & data_in[123:0])};

// // 计算跳变次数
// wire [7:0] trans_sum;
// assign trans_sum = transitions[0] + transitions[1] + transitions[2] + transitions[3] +
//                   transitions[4] + transitions[5] + transitions[6] + transitions[7] +
//                   transitions[8] + transitions[9] + transitions[10] + transitions[11] +
//                   transitions[12] + transitions[13] + transitions[14] + transitions[15] +
//                   transitions[16] + transitions[17] + transitions[18] + transitions[19] +
//                   transitions[20] + transitions[21] + transitions[22] + transitions[23] +
//                   transitions[24] + transitions[25] + transitions[26] + transitions[27] +
//                   transitions[28] + transitions[29] + transitions[30] + transitions[31] +
//                   transitions[32] + transitions[33] + transitions[34] + transitions[35] +
//                   transitions[36] + transitions[37] + transitions[38] + transitions[39] +
//                   transitions[40] + transitions[41] + transitions[42] + transitions[43] +
//                   transitions[44] + transitions[45] + transitions[46] + transitions[47] +
//                   transitions[48] + transitions[49] + transitions[50] + transitions[51] +
//                   transitions[52] + transitions[53] + transitions[54] + transitions[55] +
//                   transitions[56] + transitions[57] + transitions[58] + transitions[59] +
//                   transitions[60] + transitions[61] + transitions[62] + transitions[63] +
//                   transitions[64] + transitions[65] + transitions[66] + transitions[67] +
//                   transitions[68] + transitions[69] + transitions[70] + transitions[71] +
//                   transitions[72] + transitions[73] + transitions[74] + transitions[75] +
//                   transitions[76] + transitions[77] + transitions[78] + transitions[79] +
//                   transitions[80] + transitions[81] + transitions[82] + transitions[83] +
//                   transitions[84] + transitions[85] + transitions[86] + transitions[87] +
//                   transitions[88] + transitions[89] + transitions[90] + transitions[91] +
//                   transitions[92] + transitions[93] + transitions[94] + transitions[95] +
//                   transitions[96] + transitions[97] + transitions[98] + transitions[99] +
//                   transitions[100] + transitions[101] + transitions[102] + transitions[103] +
//                   transitions[104] + transitions[105] + transitions[106] + transitions[107] +
//                   transitions[108] + transitions[109] + transitions[110] + transitions[111] +
//                   transitions[112] + transitions[113] + transitions[114] + transitions[115] +
//                   transitions[116] + transitions[117] + transitions[118] + transitions[119] +
//                   transitions[120] + transitions[121] + transitions[122] + transitions[123] +
//                   transitions[124];



always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        // one_second_counter <= 27'd0;
        last_valve_open_count <= 32'd0;
        valve_open_per_second <= 32'd0;
    end
    else begin
        // if (one_second_counter == ONE_SECOND_COUNT - 1) begin
        if (one_sec_sig) begin
            // one_second_counter <= 27'd0;
            last_valve_open_count <= valve_open_count;
            valve_open_per_second <= valve_open_count - last_valve_open_count;
        end
        // else begin
        //     one_second_counter <= one_second_counter + 1'b1;
        // end
    end
end

// 使用时序逻辑计算跳变次数
reg [7:0] trans_sum;
reg [7:0] bit_index;

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        trans_sum <= 8'd0;
        bit_index <= 8'd0;
    end
    else begin
        if (input_valid_pos) begin
            if (bit_index == 0) begin
                trans_sum <= (~valve_cnt[valve_bit_index] & data_in[124]);
                bit_index <= bit_index + 1'b1;
            end
        end
        else if (bit_index > 8'd0 && bit_index < 8'd125) begin
            trans_sum <= trans_sum + (~data_in[125-bit_index] & data_in[124-bit_index]);
            bit_index <= bit_index + 1'b1;
        end
        else begin
            bit_index <= 8'd0;
        end
    end
end

// 使用时序逻辑计算跳变次数
// reg [7:0] trans_sum;
// reg [7:0] bit_index;

// always @(posedge clk or negedge rst_n) begin
//     if (!rst_n) begin
//         trans_sum <= 8'd0;
//         bit_index <= 8'd0;
//         valve_open_count <= 32'd0;
//         count_valid <= 1'b0;
//     end
//     else begin
//         count_valid <= 1'b0;
//         if (input_valid_pos) begin
//             trans_sum <= 8'd0;
//             bit_index <= 8'd0;
//         end
//         else if (bit_index < 8'd125) begin
//             trans_sum <= trans_sum + transitions[bit_index];
//             bit_index <= bit_index + 1'b1;
//         end
//         else if (bit_index == 8'd125) begin
//             valve_open_count <= valve_open_count + trans_sum;
//             bit_index <= bit_index + 1'b1;
//             count_valid <= 1'b1;
//         end
//     end
// end

// 输出寄存器
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        // trans_count <= 8'd0;
        valve_open_count <= 32'd0;
        // count_valid <= 1'b0;
    end
    else begin
        // count_valid <= 1'b0;
        // if (input_valid_pos) begin
        if (bit_index == 8'd125) begin
            // trans_count <= trans_sum;
            valve_open_count <= valve_open_count + trans_sum;
            // count_valid <= 1'b1;
        end
    end
end

endmodule