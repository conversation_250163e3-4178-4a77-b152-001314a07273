`timescale 1ns/1ps
module tb_bit_transition_counter;

    // 测试参数定义
    parameter CLK_PERIOD = 8; // 125MHz时钟周期
    
    // DUT信号声明
    reg clk;
    reg rst_n;
    reg input_valid;
    reg [124:0] data_in; // 修改为125位输入
    wire [7:0] trans_count;
    wire [7:0] valve_open_count;
    wire count_valid;

    // 实例化被测模块
    bit_transition_counter uut (
        .clk(clk),
        .rst_n(rst_n),
        .input_valid(input_valid),
        .data_in(data_in),
        .trans_count(trans_count),
        .valve_open_count(valve_open_count),
        .count_valid(count_valid)
    );

    // 时钟生成
    always #(CLK_PERIOD/2) clk = ~clk;

    // 测试任务：应用复位
    task apply_reset;
    begin
        rst_n = 1'b0;
        #(CLK_PERIOD*2);
        rst_n = 1'b1;
    end
    endtask

    // 修改send_packet任务参数
    task send_packet(input [124:0] data); // 数据位宽改为125位
    begin
        data_in = data;
        input_valid = 1'b1;
        #CLK_PERIOD;
        input_valid = 1'b0;
    end
    endtask

    // 测试过程
    initial begin
        // 初始化信号
        clk = 1'b0;
        rst_n = 1'b1;
        input_valid = 1'b0;
        data_in = 125'h0;
        
        // 测试用例1：复位功能验证
        $display("Test Case 1: Reset Functionality");
        apply_reset();
        if(trans_count !== 8'd0 || valve_open_count !== 8'd0 || count_valid !== 1'b0) begin
            $error("Reset failed!");
        end
        
        // 测试用例2：单次跳变检测（跨包跳变）
        $display("Test Case 2: Single Transition (Cross-packet)");
        send_packet(125'h0); // 使用125位全0
        #(CLK_PERIOD*5);
        send_packet({{120'h8000_0000_0000_0000_0000_0000_0000_00},5'b00000}); // MSB=1
        // @(posedge count_valid);
        #CLK_PERIOD;
        if(trans_count !== 8'd1) begin
            $error("Cross-packet transition not detected!");
        end
        
        // 测试用例3：最大跳变计数（交替模式）
        $display("Test Case 3: Maximum Transitions");
        // 使用125位交替模式
        send_packet({{120'h5555_5555_5555_5555_5555_5555_5555_55},5'b01010}); // 重复0x55(01010101)模式填充125位
        // @(posedge count_valid);
        #CLK_PERIOD;
        if(trans_count !== 8'd62) begin // 
            $error("Max transition count failed! Got %d", trans_count);
        end
        
        // 测试用例4：valve_open_count累加（使用125位数据）
        $display("Test Case 4: Valve Open Count Accumulation");
        send_packet(125'h1); // 1个跳变
        #CLK_PERIOD;
        send_packet(125'h3); // 1个跳变
        #CLK_PERIOD;
        // @(posedge count_valid);
        if(valve_open_count !== 8'd2) begin // 1+2=3
            $error("Valve count accumulation failed! Got %d", valve_open_count);
        end
        
        // 测试用例5：边界条件（全0/全1）
        $display("Test Case 5: Boundary Conditions");
        send_packet(125'h0);
        // @(posedge count_valid);
        #CLK_PERIOD;
        if(trans_count !== 8'd0) begin
            $error("All zero transition failed!");
        end
        
        $finish;
    end

endmodule