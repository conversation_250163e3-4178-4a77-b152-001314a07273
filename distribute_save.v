module distribute_save(
    input               sys_clk        ,  //ϵͳʱ��
    input               sys_rst_n      ,  //ϵͳ��λ���͵�ƽ��Ч

    input  wire         pulse_in,         // mcb���������ź�

    // ��udpģ����FIFO�Ľӿ�,valve_data_parserģ��ӿ�
    input  wire [7:0]  fifo_data,         // FIFO��������
    input  wire        fifo_empty,         // FIFO�ձ�־
    output wire        fifo_rd_en,         // FIFO��ʹ��
    input  wire        udp_rx_done,        // udp��������źţ�FIFO�ǿգ�

    //send_msgģ��ӿ�
    input  wire        tx_req,             // udp tx�������ź�
    input  wire        icmp_tx_busy,        // icmp tx����æ�ź�
    output wire        tx_start_en,         // udp tx��ʼ�ź�
    output wire [15:0] tx_byte_num,        // udp tx�����ֽ���
    output wire [7:0]  tx_data,             // udp tx���͵�����




    // output  wire        in_valid,    //����������Ч
    output  wire        one_pac_done, // һ���紵���������
    // output  wire [127:0] in_data,    //���������
    output wire        in_ready,    //ģ��ɽ�������
    output wire        parser_to_converter_out_valid,   //
    output wire         next_data_req, //������һ��128λ����
    output wire        write_fifo_done,
    // input  wire         tx_en,          // �ⲿ����ʹ��

    output  [31:0]  packet_type,       // ������
    output  [31:0]  packet_size,       // ����С
    // output  [31:0]  blow_delay,        // �紵��ʱ
    // output  [31:0]  trigger_time,      // �д���ʱ��
    // output  [31:0]  total_packets,     // �����ܰ���
    output          header_valid,        // ��ͷ��Ϣ��Ч
    // output  [31:0]  pac_count,         //�紵������
//    output          one_pac_done,      // one_pac_done�ź�
    output  [3:0]  lvds_out_board_1,
    output  [3:0]  lvds_out_board_2,
    output  [3:0]  lvds_out_board_3,
    output  [3:0]  lvds_out_board_4,
    output  [3:0]  lvds_out_board_5,
    output  [3:0]  lvds_out_board_6,
    output  [3:0]  lvds_out_board_7,
    output  [3:0]  lvds_out_board_8,
    output  [3:0]  lvds_out_board_9,
    output  [3:0]  lvds_out_board_10
    // output    [9:0]     fifo_wr_en      
//    output    [7:0]     ram_wr_addr    ,
//    output    [63:0]     ram_wr_data    ,
//    output    [7:0]     ram_rd_addr    ,
//    output    [63:0]     ram_rd_data  
    );

parameter CLK_FREQ = 125_000_000;    // ʱ��Ƶ��125MHz
parameter CNT_MAX = CLK_FREQ - 1;   // �������ֵ(1��)
parameter MS_CYCLES = CLK_FREQ/1000; // 1ms����ʱ��������
parameter MAX_DELAY = 600;           // �����ʱ600ms
parameter MIN_DELAY = 300;           // ��С��ʱ300ms
parameter MAX_PENDING = 8;           // ���ͬʱ����8����ʱ
parameter TIME_TOLERANCE_MS = 10;
parameter TRIGGER_TIME_DEFAULT = 800;
//wire define
wire        rd_rst  ;   //���˿ڸ�λ(ʹ��)�ź�
wire        rd_flag ;   //��������־
wire        lvds_tx_en;
wire [63:0] ram_wr_data;
// wire [63:0] ram_rd_data_1;
// wire [63:0] ram_rd_data_2;
// wire [63:0] ram_rd_data_3;
// wire [63:0] ram_rd_data_4;
// wire [63:0] ram_rd_data_5;
// wire [63:0] ram_rd_data_6;
// wire [63:0] ram_rd_data_7;
// wire [63:0] ram_rd_data_8;

wire [9:0]  ram_wr_addr;
wire [9:0]  ram_rd_addr;
wire [127:0] parser_to_converter_data;
// wire        ram_wr_en;
wire [9:0]  fifo_wr_en;
wire [31:0] udp_pac_num;
wire [31:0]  blow_delay;        // �紵��ʱ
wire [31:0]  trigger_time;      // �д���ʱ��
wire [31:0] pac_count;     //�紵������

wire [31:0] pulse_count;
wire        pulse_out;
wire        device_start_stop;
wire [31:0] valve_open_count;
wire [31:0] valve_open_per_second;
wire [31:0] fast_pulses;
wire [31:0] slow_pulses;
wire        one_sec_sig;

wire [31:0] fpga_trigger_time;
wire        fpga_trigger_method;

//*****************************************************
//**                    main code
//*****************************************************

// jet_cmd u_jet_cmd_1 (
//   .wr_data    (ram_wr_data),    // input [63:0]  ramд����
//   .wr_addr    (ram_wr_addr),    // input [7:0]  ramд��ַ
//   .wr_en      (fifo_wr_en[0] ),    // input        
//   .wr_clk     (sys_clk    ),    // input
//   .wr_rst     (~sys_rst_n ),    // input
//   .rd_addr    (ram_rd_addr),    // input [7:0]  ram����ַ
//   .rd_data    (ram_rd_data_1),    // output [63:0] ram������ 
//   .rd_clk     (sys_clk    ),    // input
//   .rd_rst     (rd_rst     )     // input
// );
// lvds_tx u_lvds_tx_1
//     (
//     .clk(sys_clk),            // input
//     .rst_n(sys_rst_n),        // input
//     .tx_en(lvds_tx_en),        // input
//     .tx_data(ram_rd_data_1),    // input
//     .tx_done(tx_done),    // output
//     .lvds_out(lvds_out)   // output
// );
// wire   sys_clk_r;
// GTP_CLKBUFR I_GTP_CLKBUFR (
// .CLKOUT (sys_clk_r),
// .CLKIN (sys_clk )
// );
top_jet_lvds u_control_board_1
    (
    .sys_clk(sys_clk),          // input
    .sys_rst_n(sys_rst_n),      // input
    .rd_rst(rd_rst),            // input
    .lvds_tx_en(lvds_tx_en),    // input
    .ram_wr_data(ram_wr_data),  // input
    .ram_wr_addr(ram_wr_addr),  // input
    .fifo_wr_en(fifo_wr_en[0]),    // input
    .ram_rd_addr(ram_rd_addr),  // input
    .lvds_out(lvds_out_board_1),        // output
    .tx_done()           // output
);


// jet_cmd u_jet_cmd_2 (
//   .wr_data    (ram_wr_data),    // input [63:0]  ramд����
//   .wr_addr    (ram_wr_addr),    // input [7:0]  ramд��ַ
//   .wr_en      (fifo_wr_en[1] ),    // input        
//   .wr_clk     (sys_clk    ),    // input
//   .wr_rst     (~sys_rst_n ),    // input
//   .rd_addr    (ram_rd_addr),    // input [7:0]  ram����ַ
//   .rd_data    (ram_rd_data_2),    // output [63:0] ram������ 
//   .rd_clk     (sys_clk    ),    // input
//   .rd_rst     (rd_rst     )     // input
// );
// lvds_tx u_lvds_tx_2
//     (
//     .clk(sys_clk),            // input
//     .rst_n(sys_rst_n),        // input
//     .tx_en(lvds_tx_en),        // input
//     .tx_data(ram_rd_data_2),    // input
//     .tx_done(tx_done),    // output
//     .lvds_out(lvds_out)   // output
// );
top_jet_lvds u_control_board_2
    (
    .sys_clk(sys_clk),          // input
    .sys_rst_n(sys_rst_n),      // input
    .rd_rst(rd_rst),            // input
    .lvds_tx_en(lvds_tx_en),    // input
    .ram_wr_data(ram_wr_data),  // input
    .ram_wr_addr(ram_wr_addr),  // input
    .fifo_wr_en(fifo_wr_en[1]),    // input
    .ram_rd_addr(ram_rd_addr),  // input
    .lvds_out(lvds_out_board_2),        // output
    .tx_done()           // output
);

// jet_cmd u_jet_cmd_3 (
//   .wr_data    (ram_wr_data),    // input [63:0]  ramд����
//   .wr_addr    (ram_wr_addr),    // input [7:0]  ramд��ַ
//   .wr_en      (fifo_wr_en[2] ),    // input        
//   .wr_clk     (sys_clk    ),    // input
//   .wr_rst     (~sys_rst_n ),    // input
//   .rd_addr    (ram_rd_addr),    // input [7:0]  ram����ַ
//   .rd_data    (ram_rd_data_3),    // output [63:0] ram������ 
//   .rd_clk     (sys_clk    ),    // input
//   .rd_rst     (rd_rst     )     // input
// );
// lvds_tx u_lvds_tx_3
//     (
//     .clk(sys_clk),            // input
//     .rst_n(sys_rst_n),        // input
//     .tx_en(lvds_tx_en),        // input
//     .tx_data(ram_rd_data_3),    // input
//     .tx_done(tx_done),    // output
//     .lvds_out(lvds_out)   // output
// );
top_jet_lvds u_control_board_3
    (
    .sys_clk(sys_clk),          // input
    .sys_rst_n(sys_rst_n),      // input
    .rd_rst(rd_rst),            // input
    .lvds_tx_en(lvds_tx_en),    // input
    .ram_wr_data(ram_wr_data),  // input
    .ram_wr_addr(ram_wr_addr),  // input
    .fifo_wr_en(fifo_wr_en[2]),    // input
    .ram_rd_addr(ram_rd_addr),  // input
    .lvds_out(lvds_out_board_3),        // output
    .tx_done()           // output
);
// jet_cmd u_jet_cmd_4 (
//   .wr_data    (ram_wr_data),    // input [63:0]  ramд����
//   .wr_addr    (ram_wr_addr),    // input [7:0]  ramд��ַ
//   .wr_en      (fifo_wr_en[3] ),    // input        
//   .wr_clk     (sys_clk    ),    // input
//   .wr_rst     (~sys_rst_n ),    // input
//   .rd_addr    (ram_rd_addr),    // input [7:0]  ram����ַ
//   .rd_data    (ram_rd_data_4),    // output [63:0] ram������ 
//   .rd_clk     (sys_clk    ),    // input
//   .rd_rst     (rd_rst     )     // input
// );
// lvds_tx u_lvds_tx_4
//     (
//     .clk(sys_clk),            // input
//     .rst_n(sys_rst_n),        // input
//     .tx_en(lvds_tx_en),        // input
//     .tx_data(ram_rd_data_4),    // input
//     .tx_done(tx_done),    // output
//     .lvds_out(lvds_out)   // output
// );
top_jet_lvds u_control_board_4
    (
    .sys_clk(sys_clk),          // input
    .sys_rst_n(sys_rst_n),      // input
    .rd_rst(rd_rst),            // input
    .lvds_tx_en(lvds_tx_en),    // input
    .ram_wr_data(ram_wr_data),  // input
    .ram_wr_addr(ram_wr_addr),  // input
    .fifo_wr_en(fifo_wr_en[3]),    // input
    .ram_rd_addr(ram_rd_addr),  // input
    .lvds_out(lvds_out_board_4),        // output
    .tx_done()           // output
);

// jet_cmd u_jet_cmd_5 (
//   .wr_data    (ram_wr_data),    // input [63:0]  ramд����
//   .wr_addr    (ram_wr_addr),    // input [7:0]  ramд��ַ
//   .wr_en      (fifo_wr_en[4] ),    // input        
//   .wr_clk     (sys_clk    ),    // input
//   .wr_rst     (~sys_rst_n ),    // input
//   .rd_addr    (ram_rd_addr),    // input [7:0]  ram����ַ
//   .rd_data    (ram_rd_data_5),    // output [63:0] ram������ 
//   .rd_clk     (sys_clk    ),    // input
//   .rd_rst     (rd_rst     )     // input
// );
// lvds_tx u_lvds_tx_5
//     (
//     .clk(sys_clk),            // input
//     .rst_n(sys_rst_n),        // input
//     .tx_en(lvds_tx_en),        // input
//     .tx_data(ram_rd_data_5),    // input
//     .tx_done(tx_done),    // output
//     .lvds_out(lvds_out)   // output
// );
top_jet_lvds u_control_board_5
    (
    .sys_clk(sys_clk),          // input
    .sys_rst_n(sys_rst_n),      // input
    .rd_rst(rd_rst),            // input
    .lvds_tx_en(lvds_tx_en),    // input
    .ram_wr_data(ram_wr_data),  // input
    .ram_wr_addr(ram_wr_addr),  // input
    .fifo_wr_en(fifo_wr_en[4]),    // input
    .ram_rd_addr(ram_rd_addr),  // input
    .lvds_out(lvds_out_board_5),        // output
    .tx_done()           // output
);
// jet_cmd u_jet_cmd_6 (
//   .wr_data    (ram_wr_data),    // input [63:0]  ramд����
//   .wr_addr    (ram_wr_addr),    // input [7:0]  ramд��ַ
//   .wr_en      (fifo_wr_en[5] ),    // input        
//   .wr_clk     (sys_clk    ),    // input
//   .wr_rst     (~sys_rst_n ),    // input
//   .rd_addr    (ram_rd_addr),    // input [7:0]  ram����ַ
//   .rd_data    (ram_rd_data_6),    // output [63:0] ram������ 
//   .rd_clk     (sys_clk    ),    // input
//   .rd_rst     (rd_rst     )     // input
// );
// lvds_tx u_lvds_tx_6
//     (
//     .clk(sys_clk),            // input
//     .rst_n(sys_rst_n),        // input
//     .tx_en(lvds_tx_en),        // input
//     .tx_data(ram_rd_data_6),    // input
//     .tx_done(tx_done),    // output
//     .lvds_out(lvds_out)   // output
// );
top_jet_lvds u_control_board_6
    (
    .sys_clk(sys_clk),          // input
    .sys_rst_n(sys_rst_n),      // input
    .rd_rst(rd_rst),            // input
    .lvds_tx_en(lvds_tx_en),    // input
    .ram_wr_data(ram_wr_data),  // input
    .ram_wr_addr(ram_wr_addr),  // input
    .fifo_wr_en(fifo_wr_en[5]),    // input
    .ram_rd_addr(ram_rd_addr),  // input
    .lvds_out(lvds_out_board_6),        // output
    .tx_done()           // output
);
// jet_cmd u_jet_cmd_7 (
//   .wr_data    (ram_wr_data),    // input [63:0]  ramд����
//   .wr_addr    (ram_wr_addr),    // input [7:0]  ramд��ַ
//   .wr_en      (fifo_wr_en[6] ),    // input        
//   .wr_clk     (sys_clk    ),    // input
//   .wr_rst     (~sys_rst_n ),    // input
//   .rd_addr    (ram_rd_addr),    // input [7:0]  ram����ַ
//   .rd_data    (ram_rd_data_7),    // output [63:0] ram������ 
//   .rd_clk     (sys_clk    ),    // input
//   .rd_rst     (rd_rst     )     // input
// );
// lvds_tx u_lvds_tx_7
//     (
//     .clk(sys_clk),            // input
//     .rst_n(sys_rst_n),        // input
//     .tx_en(lvds_tx_en),        // input
//     .tx_data(ram_rd_data_7),    // input
//     .tx_done(tx_done),    // output
//     .lvds_out(lvds_out)   // output
// );
top_jet_lvds u_control_board_7
    (
    .sys_clk(sys_clk),          // input
    .sys_rst_n(sys_rst_n),      // input
    .rd_rst(rd_rst),            // input
    .lvds_tx_en(lvds_tx_en),    // input
    .ram_wr_data(ram_wr_data),  // input
    .ram_wr_addr(ram_wr_addr),  // input
    .fifo_wr_en(fifo_wr_en[6]),    // input
    .ram_rd_addr(ram_rd_addr),  // input
    .lvds_out(lvds_out_board_7),        // output
    .tx_done()           // output
);
// jet_cmd u_jet_cmd_8 (
//   .wr_data    (ram_wr_data),    // input [63:0]  ramд����
//   .wr_addr    (ram_wr_addr),    // input [7:0]  ramд��ַ
//   .wr_en      (fifo_wr_en[7] ),    // input        
//   .wr_clk     (sys_clk    ),    // input
//   .wr_rst     (~sys_rst_n ),    // input
//   .rd_addr    (ram_rd_addr),    // input [7:0]  ram����ַ
//   .rd_data    (ram_rd_data_8),    // output [63:0] ram������ 
//   .rd_clk     (sys_clk    ),    // input
//   .rd_rst     (rd_rst     )     // input
// );
// lvds_tx u_lvds_tx_8
//     (
//     .clk(sys_clk),            // input
//     .rst_n(sys_rst_n),        // input
//     .tx_en(lvds_tx_en),        // input
//     .tx_data(ram_rd_data_8),    // input
//     .tx_done(tx_done),    // output
//     .lvds_out(lvds_out)   // output
// );
top_jet_lvds u_control_board_8
    (
    .sys_clk(sys_clk),          // input
    .sys_rst_n(sys_rst_n),      // input
    .rd_rst(rd_rst),            // input
    .lvds_tx_en(lvds_tx_en),    // input
    .ram_wr_data(ram_wr_data),  // input
    .ram_wr_addr(ram_wr_addr),  // input
    .fifo_wr_en(fifo_wr_en[7]),    // input
    .ram_rd_addr(ram_rd_addr),  // input
    .lvds_out(lvds_out_board_8),        // output
    .tx_done()           // output
);

top_jet_lvds u_control_board_9
    (
    .sys_clk(sys_clk),          // input
    .sys_rst_n(sys_rst_n),      // input
    .rd_rst(rd_rst),            // input
    .lvds_tx_en(lvds_tx_en),    // input
    .ram_wr_data(ram_wr_data),  // input
    .ram_wr_addr(ram_wr_addr),  // input
    .fifo_wr_en(fifo_wr_en[8]),    // input
    .ram_rd_addr(ram_rd_addr),  // input
    .lvds_out(lvds_out_board_9),        // output
    .tx_done()           // output
);


top_jet_lvds u_control_board_10
    (
    .sys_clk(sys_clk),          // input
    .sys_rst_n(sys_rst_n),      // input
    .rd_rst(rd_rst),            // input
    .lvds_tx_en(lvds_tx_en),    // input
    .ram_wr_data(ram_wr_data),  // input
    .ram_wr_addr(ram_wr_addr),  // input
    .fifo_wr_en(fifo_wr_en[9]),    // input
    .ram_rd_addr(ram_rd_addr),  // input
    .lvds_out(lvds_out_board_10),        // output
    .tx_done()           // output
);

pulse_delay
    #(
    .CLK_FREQ(CLK_FREQ),
    .MS_CYCLES(MS_CYCLES),
    .MAX_DELAY(MAX_DELAY),
    .MIN_DELAY(MIN_DELAY),
    .MAX_PENDING(MAX_PENDING)
    ) u_pulse_delay
    (
    .clk(sys_clk),                  // input
    .rst_n(sys_rst_n),              // input
    .pulse_in(pulse_in),        // input
    .device_start_stop(device_start_stop),
    .delay_time(blow_delay),    // input
    .pulse_out(pulse_out),      // output
    .pulse_count(pulse_count)   // output
);

pulse_monitor
    #(
    .TIME_TOLERANCE_MS(TIME_TOLERANCE_MS)
    ) u_pulse_monitor
    (
    .clk(sys_clk),                              // input
    .rst_n(sys_rst_n),                          // input
    .pulse_in(pulse_out),                    // input
    .device_start_stop(device_start_stop),    // input
    .total_pulses(),            // output
    .lost_pulses(),              // output
    .fast_pulses(fast_pulses),              // output
    .theory_pulses(),          // output
    .slow_pulses(slow_pulses)               // output
);

lvds_tx_ctrl #(
    .CLK_FREQ(CLK_FREQ),
    .US_CYCLES(),
    .TRIGGER_TIME_DEFAULT(TRIGGER_TIME_DEFAULT)
) u_lvds_tx_ctrl
(
    .clk(sys_clk),                  // input
    .rst_n(sys_rst_n),              // input
    .tx_en(pulse_out),              // input
    .tx_done(1'b0),          // input
    .fifo_empty(1'b0),    // input
    .pulse_count(pulse_count),    // input
    .trigger_time(trigger_time),    // input
    .udp_pac_num(udp_pac_num),    // input
    .fifo_rd_rst(rd_rst),  // output
    .addr(ram_rd_addr),      // output
    .device_start(device_start_stop),     // �豸�����ź�
    .lvds_tx_en(lvds_tx_en)     // output
);

valve_data_parser u_valve_data_parser
    (
    .clk(sys_clk),                          // input
    .rst_n(sys_rst_n),                      // input

    .fifo_data(fifo_data),              // input
    .fifo_empty(fifo_empty),            // input
    .fifo_rd_en(fifo_rd_en),            // output

    .udp_rx_done(udp_rx_done),          // input

    .converter_ready(in_ready),  // input
    .next_data_req(next_data_req),      // input
    .out_valid(parser_to_converter_out_valid),              // output
    .out_data(parser_to_converter_data),                // output

    .packet_type(packet_type),          // output
    .packet_size(packet_size),          // output
    .device_start_stop(device_start_stop),//output
    .blow_delay(blow_delay),            // output
    .trigger_time(trigger_time),        // output
    .total_packets(),                   // output
    .header_valid(header_valid),        // output
    .pac_count(pac_count),              // output
    .udp_pac_num(udp_pac_num),          // output
    .fpga_trigger_time(fpga_trigger_time),
    .fpga_trigger_method(fpga_trigger_method),
    .one_pac_done(one_pac_done)         // output
);

bit_transition_counter u_bit_transition_counter
    (
    .clk(sys_clk),                            // input
    .rst_n(sys_rst_n),                        // input
    .input_valid(parser_to_converter_out_valid),            // input
    .data_in(parser_to_converter_data[127:3]),                    // input
    .one_pac_done(one_pac_done),               // input
    .one_sec_sig(one_sec_sig),
    // .trans_count(),            // output
    .valve_open_count(valve_open_count),  // output
    // .count_valid(),             // output
    .valve_open_per_second(valve_open_per_second) // output
);

// second_counter
//     #(
//     .CLK_FREQ(CLK_FREQ),
//     .CNT_MAX(CNT_MAX)
//     ) u_second_counter
//     (
//     .clk(sys_clk),                                      // input
//     .rst_n(sys_rst_n),                                  // input
//     .valve_open_count(valve_open_count),            // input
//     .valve_open_per_second(valve_open_per_second)   // output
// );

send_msg u_send_msg
    (
    .sys_clk(sys_clk),                              // input
    .sys_rst_n(sys_rst_n),                          // input
    .tx_req(tx_req),                                // input
    .icmp_tx_busy(icmp_tx_busy),                    // input
    .one_sec_sig(one_sec_sig),
    .one_pac_done(one_pac_done),
    .device_start_stop(device_start_stop),
    .blow_delay_x(blow_delay),                     // input
    .line_trigger_time(trigger_time),              // input
    .total_trigger_signals(pulse_count),           // input

    .lost_triggers_a(fast_pulses),              // input
    .total_data_packets(udp_pac_num),        // input
    .valve_freq(valve_open_per_second),                        // input
    .valve_total(valve_open_count),                      // input
    .lost_triggers_b(slow_pulses),              // input
    .lost_packets_b(32'b0),                // input
    .reserved_field(32'b0),                // input

    .tx_start_en(tx_start_en),                      // output
    .tx_byte_num(tx_byte_num),                      // output
    .tx_data(tx_data)                               // output
);

bit_to_64bit_converter u_converter
    (
    .clk(sys_clk),                      // input
    .rst_n(sys_rst_n),                  // input
    .in_valid(parser_to_converter_out_valid),            // input
    .one_pac_done(one_pac_done),    // input
    .last_bit_of_pac_count(pac_count[0]),  // input
    .in_data(parser_to_converter_data),              // input[127:0]
    .in_ready(in_ready),            // output
    .out_valid(write_fifo_done),          // output
    .out_data(ram_wr_data),            // output[63:0]
    .fifo_wr_en(fifo_wr_en),        // output[7:0]
    .udp_pac_num(udp_pac_num),
    .device_start_stop(device_start_stop),
    .out_addr(ram_wr_addr),            // output[7:0]
    .next_data_req(next_data_req)   // output
);

second_counter #(
    .CLK_FREQ(CLK_FREQ),
    .CNT_MAX(CNT_MAX)
) u_sec_cnt (
    .clk(sys_clk),                  // input
    .rst_n(sys_rst_n),              // input
    .one_sec_sig(one_sec_sig)   // output
);

endmodule