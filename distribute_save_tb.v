`timescale 1ns/1ps

module distribute_save_tb();

// 时钟周期参数 (125MHz = 8ns)
localparam CLK_PERIOD = 8;
localparam CLK_FREQ = 125_000_000;

// 测试信号声明
reg               sys_clk;
reg               sys_rst_n;

// 脉冲输入信号
reg               pulse_in;

// UDP FIFO接口信号
reg  [7:0]        fifo_data;
reg               fifo_empty;
wire              fifo_rd_en;
reg               udp_rx_done;

// UDP发送接口信号
reg               tx_req;
reg               icmp_tx_busy;
wire              tx_start_en;
wire [15:0]       tx_byte_num;
wire [7:0]        tx_data;

// 输出信号
wire              one_pac_done;
wire              in_ready;
wire              parser_to_converter_out_valid;
wire              next_data_req;
wire              write_fifo_done;

wire [31:0]       packet_type;
wire [31:0]       packet_size;
wire              header_valid;

// LVDS输出信号
wire [3:0]        lvds_out_board_1;
wire [3:0]        lvds_out_board_2;
wire [3:0]        lvds_out_board_3;
wire [3:0]        lvds_out_board_4;
wire [3:0]        lvds_out_board_5;
wire [3:0]        lvds_out_board_6;
wire [3:0]        lvds_out_board_7;
wire [3:0]        lvds_out_board_8;
wire [3:0]        lvds_out_board_9;
wire [3:0]        lvds_out_board_10;

reg grs_n;
GTP_GRS GRS_INST(
        .GRS_N(grs_n)
);

initial begin
    grs_n = 1'b0;
    #500 grs_n = 1'b1;
end


// 测试数据存储
reg [7:0] test_packet [0:3327]; // 存储完整的测试数据包
reg [7:0] test_data [0:32];
integer data_index;
integer packet_count;

// 脉冲生成相关
reg [31:0] pulse_interval_counter;
localparam PULSE_INTERVAL = CLK_FREQ / 10; // 100ms间隔

// 实例化被测模块
distribute_save u_distribute_save (
    .sys_clk(sys_clk),
    .sys_rst_n(sys_rst_n),
    
    // 脉冲输入
    .pulse_in(pulse_in),
    
    // UDP FIFO接口
    .fifo_data(fifo_data),
    .fifo_empty(fifo_empty),
    .fifo_rd_en(fifo_rd_en),
    .udp_rx_done(udp_rx_done),
    
    // UDP发送接口
    .tx_req(tx_req),
    .icmp_tx_busy(icmp_tx_busy),
    .tx_start_en(tx_start_en),
    .tx_byte_num(tx_byte_num),
    .tx_data(tx_data),
    
    // 输出信号
    .one_pac_done(one_pac_done),
    .in_ready(in_ready),
    .parser_to_converter_out_valid(parser_to_converter_out_valid),
    .next_data_req(next_data_req),
    .write_fifo_done(write_fifo_done),
    
    .packet_type(packet_type),
    .packet_size(packet_size),
    .header_valid(header_valid),
    
    // LVDS输出
    .lvds_out_board_1(lvds_out_board_1),
    .lvds_out_board_2(lvds_out_board_2),
    .lvds_out_board_3(lvds_out_board_3),
    .lvds_out_board_4(lvds_out_board_4),
    .lvds_out_board_5(lvds_out_board_5),
    .lvds_out_board_6(lvds_out_board_6),
    .lvds_out_board_7(lvds_out_board_7),
    .lvds_out_board_8(lvds_out_board_8),
    .lvds_out_board_9(lvds_out_board_9),
    .lvds_out_board_10(lvds_out_board_10)
);

// 时钟生成
initial begin
    sys_clk = 0;
    forever #(CLK_PERIOD/2) sys_clk = ~sys_clk;
end

// 脉冲生成任务 - 模拟100ms间隔的脉冲
always @(posedge sys_clk or negedge sys_rst_n) begin
    if (!sys_rst_n) begin
        pulse_interval_counter <= 32'd0;
        pulse_in <= 1'b0;
    end else begin
        pulse_interval_counter <= pulse_interval_counter + 1'b1;
        
        if (pulse_interval_counter == PULSE_INTERVAL - 1) begin
            pulse_in <= 1'b1;
            pulse_interval_counter <= 32'd0;
        end else if (pulse_interval_counter == 32'd10) begin
            pulse_in <= 1'b0;
        end
    end
end

task init_cmd_packet;
    integer i;
    begin
        test_data[0] = 8'h00; test_data[1] = 8'h00; 
        test_data[2] = 8'h00; test_data[3] = 8'h00;
        
        // 设备开始
        test_data[4] = 8'h00; test_data[5] = 8'h00; 
        test_data[6] = 8'h00; test_data[7] = 8'h01;
        
        // 喷吹延时: 500*800us
        test_data[8] = 8'h00; test_data[9] = 8'h00;
        test_data[10] = 8'h01; test_data[11] = 8'hF4;
        
        // 行触发时间: 800
        test_data[12] = 8'h00; test_data[13] = 8'h00;
        test_data[14] = 8'h03; test_data[15] = 8'h20;
        
        // 数据总包数: 2
        test_data[16] = 8'h00; test_data[17] = 8'h00;
        test_data[18] = 8'h00; test_data[19] = 8'h02;
        
        // 空字段 (12 bytes)
        for(i = 20; i < 32; i = i + 1) begin
            test_data[i] = 8'h00;
        end
    end
endtask


// 初始化测试数据包
task init_test_packet;
    integer i;
    begin
        // 包头数据 (32字节)
        // 包类型 (4字节) - 数据包类型
        test_packet[0] = 8'h00;
        test_packet[1] = 8'h00;
        test_packet[2] = 8'h00;
        test_packet[3] = 8'h01;
        
        // 包大小 (4字节)
        test_packet[4] = 8'h00;
        test_packet[5] = 8'h00;
        test_packet[6] = 8'h05;
        test_packet[7] = 8'h20; // 1280字节数据
        
        // 喷吹延时 (4字节)
        test_packet[8] = 8'h00;
        test_packet[9] = 8'h00;
        test_packet[10] = 8'h01;
        test_packet[11] = 8'hF4; // 启动
        
        // 行触发时间 (4字节) - 800us
        test_packet[12] = 8'h00;
        test_packet[13] = 8'h00;
        test_packet[14] = 8'h03;
        test_packet[15] = 8'h20;
        
        //  数据总包数 (4字节)
        test_packet[16] = 8'h00;
        test_packet[17] = 8'h00;
        test_packet[18] = 8'h00;
        test_packet[19] = 8'h02;
        
        // UDP总包数 (4字节)
        test_packet[20] = 8'h00;
        test_packet[21] = 8'h00;
        test_packet[22] = 8'h00;
        test_packet[23] = 8'h01; 
        
        // 包头剩余字段填充0
        for (i = 24; i < 32; i = i + 1) begin
            test_packet[i] = 8'h00;
        end
        
        // 喷吹指令数据 (1280 bytes)
            for(i = 32; i < 1312; i = i + 1) begin
                test_packet[i] = i[7:0]; // 使用递增数据方便验证
            end
        
        // 包头数据 (32 bytes)
        // 包类型: 2 (第2包阀数据)
        test_packet[1312] = 8'h00; test_packet[1313] = 8'h00; 
        test_packet[1314] = 8'h00; test_packet[1315] = 8'h02;
        
        // 包大小: 352 bytes
        test_packet[1316] = 8'h00; test_packet[1317] = 8'h00; 
        test_packet[1318] = 8'h01; test_packet[1319] = 8'h60;
        
        // 喷吹延时: 2000
        test_packet[1320] = 8'h00; test_packet[1321] = 8'h00;
        test_packet[1322] = 8'h01; test_packet[1323] = 8'hF4;
        
        // 行触发时间: 800
        test_packet[1324] = 8'h00; test_packet[1325] = 8'h00;
        test_packet[1326] = 8'h03; test_packet[1327] = 8'h20;
        
        // 数据总包数: 2
        test_packet[1328] = 8'h00; test_packet[1329] = 8'h00;
        test_packet[1330] = 8'h00; test_packet[1331] = 8'h02;

        // UDP总包数: 1
        test_packet[1332] = 8'h00; test_packet[1333] = 8'h00;
        test_packet[1334] = 8'h00; test_packet[1335] = 8'h01;

        // 空字段 (8 bytes)
        for(i = 1336; i < 1344; i = i + 1) begin
            test_packet[i] = 8'h00;
        end

        // 喷吹指令数据 (320 bytes)
        for(i = 1344; i < 1664; i = i + 1) begin
            test_packet[i] = i[7:0]; // 使用递增数据方便验证
        end
/*************************************第二包*****************************************/
        // 包头数据 (32字节)
        // 包类型 (4字节) - 数据包类型
        test_packet[1664] = 8'h00;
        test_packet[1665] = 8'h00;
        test_packet[1666] = 8'h00;
        test_packet[1667] = 8'h01;
        
        // 包大小 (4字节)
        test_packet[1668] = 8'h00;
        test_packet[1669] = 8'h00;
        test_packet[1670] = 8'h05;
        test_packet[1671] = 8'h20; // 1280字节数据
        
        // 喷吹延时 (4字节)
        test_packet[1672] = 8'h00;
        test_packet[1673] = 8'h00;
        test_packet[1674] = 8'h01;
        test_packet[1675] = 8'hF4; // 启动
        
        // 行触发时间 (4字节) - 800us
        test_packet[1676] = 8'h00;
        test_packet[1677] = 8'h00;
        test_packet[1678] = 8'h03;
        test_packet[1679] = 8'h20;
        
        //  数据总包数 (4字节)
        test_packet[1680] = 8'h00;
        test_packet[1681] = 8'h00;
        test_packet[1682] = 8'h00;
        test_packet[1683] = 8'h02;
        
        // UDP总包数 (4字节)
        test_packet[1684] = 8'h00;
        test_packet[1685] = 8'h00;
        test_packet[1686] = 8'h00;
        test_packet[1687] = 8'h02; 
        
        // 包头剩余字段填充0
        for (i = 1688; i < 1696; i = i + 1) begin
            test_packet[i] = 8'h00;
        end
        
        // 喷吹指令数据 (1280 bytes)
        for(i = 1696; i < 2976; i = i + 1) begin
            test_packet[i] = i[7:0]; // 使用递增数据方便验证
        end

        test_packet[2976] = 8'h00; test_packet[2977] = 8'h00; 
        test_packet[2978] = 8'h00; test_packet[2979] = 8'h02;
        
        // 包大小: 352 bytes
        test_packet[2980] = 8'h00; test_packet[2981] = 8'h00; 
        test_packet[2982] = 8'h01; test_packet[2983] = 8'h60;
        
        // 喷吹延时: 2000
        test_packet[2984] = 8'h00; test_packet[2985] = 8'h00;
        test_packet[2986] = 8'h01; test_packet[2987] = 8'hF4;
        
        // 行触发时间: 800
        test_packet[2988] = 8'h00; test_packet[2989] = 8'h00;
        test_packet[2990] = 8'h03; test_packet[2991] = 8'h20;
        
        // 数据总包数: 2
        test_packet[2992] = 8'h00; test_packet[2993] = 8'h00;
        test_packet[2994] = 8'h00; test_packet[2995] = 8'h02;

        // UDP总包数: 1
        test_packet[2996] = 8'h00; test_packet[2997] = 8'h00;
        test_packet[2998] = 8'h00; test_packet[2999] = 8'h02;

        // 空字段 (8 bytes)
        for(i = 3000; i < 3008; i = i + 1) begin
            test_packet[i] = 8'h00;
        end

        // 喷吹指令数据 (320 bytes)
        for(i = 3008; i < 3328; i = i + 1) begin
            test_packet[i] = i[7:0]; // 使用递增数据方便验证
        end

    end
endtask

task init_test_packet2;
    integer i;
    begin
        // 包头数据 (32字节)
        // 包类型 (4字节) - 数据包类型
        test_packet[0] = 8'h00;
        test_packet[1] = 8'h00;
        test_packet[2] = 8'h00;
        test_packet[3] = 8'h01;
        
        // 包大小 (4字节)
        test_packet[4] = 8'h00;
        test_packet[5] = 8'h00;
        test_packet[6] = 8'h05;
        test_packet[7] = 8'h20; // 1280字节数据
        
        // 喷吹延时 (4字节)
        test_packet[8] = 8'h00;
        test_packet[9] = 8'h00;
        test_packet[10] = 8'h01;
        test_packet[11] = 8'hF4; // 启动
        
        // 行触发时间 (4字节) - 800us
        test_packet[12] = 8'h00;
        test_packet[13] = 8'h00;
        test_packet[14] = 8'h03;
        test_packet[15] = 8'h20;
        
        //  数据总包数 (4字节)
        test_packet[16] = 8'h00;
        test_packet[17] = 8'h00;
        test_packet[18] = 8'h00;
        test_packet[19] = 8'h02;
        
        // UDP总包数 (4字节)
        test_packet[20] = 8'h00;
        test_packet[21] = 8'h00;
        test_packet[22] = 8'h00;
        test_packet[23] = 8'h02; 
        
        // 包头剩余字段填充0
        for (i = 24; i < 32; i = i + 1) begin
            test_packet[i] = 8'h00;
        end
        
        // 喷吹指令数据 (1280 bytes)
        for(i = 32; i < 1312; i = i + 1) begin
            test_packet[i] = i[7:0]; // 使用递增数据方便验证
        end
        
        // 包头数据 (32 bytes)
        // 包类型: 2 (第2包阀数据)
        test_packet[1312] = 8'h00; test_packet[1313] = 8'h00; 
        test_packet[1314] = 8'h00; test_packet[1315] = 8'h02;
        
        // 包大小: 352 bytes
        test_packet[1316] = 8'h00; test_packet[1317] = 8'h00; 
        test_packet[1318] = 8'h01; test_packet[1319] = 8'h60;
        
        // 喷吹延时: 2000
        test_packet[1320] = 8'h00; test_packet[1321] = 8'h00;
        test_packet[1322] = 8'h01; test_packet[1323] = 8'hF4;
        
        // 行触发时间: 800
        test_packet[1324] = 8'h00; test_packet[1325] = 8'h00;
        test_packet[1326] = 8'h03; test_packet[1327] = 8'h20;
        
        // 数据总包数: 2
        test_packet[1328] = 8'h00; test_packet[1329] = 8'h00;
        test_packet[1330] = 8'h00; test_packet[1331] = 8'h02;

        // UDP总包数: 1
        test_packet[1332] = 8'h00; test_packet[1333] = 8'h00;
        test_packet[1334] = 8'h00; test_packet[1335] = 8'h02;

        // 空字段 (8 bytes)
        for(i = 1336; i < 1344; i = i + 1) begin
            test_packet[i] = 8'h00;
        end

        // 喷吹指令数据 (320 bytes)
        for(i = 1344; i < 1664; i = i + 1) begin
            test_packet[i] = i[7:0]; // 使用递增数据方便验证
        end
    end
endtask

// FIFO数据发送任务
task send_packet_data;
    integer i;
    begin
        $display("开始发送数据包...");
        fifo_empty = 1'b0;
        udp_rx_done = 1'b1;
        #(CLK_PERIOD * 2);
        udp_rx_done = 1'b0;
        
        for (i = 0; i < 3328; i = i + 1) begin
            @(posedge sys_clk);
            if (fifo_rd_en) begin
                fifo_data = test_packet[i];
                #(CLK_PERIOD);
            end else begin
                i = i - 1; // 如果没有读使能，重试当前数据
            end
        end
        
        fifo_empty = 1'b1;
        $display("数据包发送完成");
    end
endtask

task send_cmd_data;
    integer i;
    begin 
        $display("开始发送控制包");
        fifo_empty = 1'b0;
        udp_rx_done = 1'b1;
        #(CLK_PERIOD * 2);
        udp_rx_done = 1'b0;

        for (i = 0; i < 32; i = i + 1) begin
            @(posedge sys_clk);
            if (fifo_rd_en) begin
                fifo_data = test_data[i];
                #(CLK_PERIOD);
            end else begin
                i = i - 1; // 如果没有读使能，重试当前数据
            end
        end
        
        fifo_empty = 1'b1;
        $display("控制包发送完成");
    end
endtask

// UDP发送请求模拟
task simulate_udp_tx_requests;
    integer i;
    begin
        wait(tx_start_en);
        $display("开始UDP发送过程...");
        
        for (i = 0; i < 48; i = i + 1) begin // 48字节数据
            @(posedge sys_clk);
            tx_req = 1'b1;
            #(CLK_PERIOD);
            tx_req = 1'b0;
            #(CLK_PERIOD * 3); // 模拟发送间隔
        end
        
        $display("UDP发送完成");
    end
endtask

// 主测试流程
initial begin
    // 初始化信号
    sys_rst_n = 1'b0;
    pulse_in = 1'b0;
    fifo_data = 8'h00;
    fifo_empty = 1'b1;
    udp_rx_done = 1'b0;
    tx_req = 1'b0;
    icmp_tx_busy = 1'b0;
    data_index = 0;
    packet_count = 0;
    
    // 初始化命令包
    init_cmd_packet();
    // 初始化测试数据
    init_test_packet();
    
    // 复位释放
    #(CLK_PERIOD * 10);
    sys_rst_n = 1'b1;
    $display("复位释放，开始测试...");
    
    // 等待系统稳定
    #(CLK_PERIOD * 20);
    
    // 测试场景1：发送控制包（启动设备）
    $display("=== testcase 1:send cmd packet ===");
    fork
        // send_packet_data();
        send_cmd_data();
        // simulate_udp_tx_requests();
    join
    
    // 等待包处理完成
    // wait(one_pac_done);
    $display("cmd packet send done========================");
    
    // 等待设备启动和脉冲开始
    #(CLK_PERIOD * 500);
    
    // 测试场景2：发送数据包
    $display("=== testcase 2:send data packet ===");
    // 修改测试数据为数据包类型
    // test_packet[3] = 8'h02; // 数据包类型

    fork
        send_packet_data();
    join
    
    // repeat(3) begin // 发送3个数据包
    //     packet_count = packet_count + 1;
    //     $display("发送第%d个数据包", packet_count);
        
    //     fork
    //         send_packet_data();
    //         simulate_udp_tx_requests();
    //     join
        
    //     wait(one_pac_done);
    //     $display("第%d个数据包处理完成", packet_count);
        
    //     // 等待脉冲间隔
    //     #(CLK_PERIOD * 5000);
    // end
    
    // 测试场景3：观察LVDS输出
    $display("=== testcasere 3:observe LVDS output ===");
    #(CLK_PERIOD * 50000); // 等待足够长时间观察LVDS输出
    
    // 测试场景4：测试脉冲监控功能
    $display("=== testcase 4:test pulse monitor function ===");
    // 模拟不规律脉冲
    repeat(5) begin
        #(CLK_PERIOD * (PULSE_INTERVAL + $random % 10000)); // 随机延时
        pulse_in = 1'b1;
        #(CLK_PERIOD * 10);
        pulse_in = 1'b0;
    end
    
    // 等待观察结果
    #(CLK_PERIOD * 100000);
    
    $display("=== 测试完成 ===");
    $finish;
end

// 监控和显示
initial begin
    $monitor("Time=%0t: packet_type=%h, packet_size=%h, header_valid=%b, one_pac_done=%b", 
             $time, packet_type, packet_size, header_valid, one_pac_done);
end

// 波形文件生成
initial begin
    $dumpfile("distribute_save_tb.vcd");
    $dumpvars(0, distribute_save_tb);
end

// 超时保护
initial begin
    #50_000_000; // 50ms超时
    $display("测试超时！");
    $finish;
end

endmodule