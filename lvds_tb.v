`timescale 1ns/1ps

module lvds_tb;
    // 时钟周期参数
    localparam CLK_PERIOD = 8;      // 125MHz系统时钟周期 (8ns)
    
    // 测试信号定义
    reg           clk;              // 系统时钟 125MHz
    reg           rst_n;            // 异步复位，低电平有效
    reg           tx_en;            // 发送使能，高电平有效
    reg  [63:0]   tx_data;          // 输入数据，8个字节
    wire [3:0]    lvds_out;         // LVDS输出，包含随路时钟和数据
    
    // 测试结果记录
    reg  [63:0]   received_data;    // 接收到的数据
    integer       bit_index;        // 接收位索引
    integer       error_count;      // 错误计数
    
    // 实例化被测模块
    lvds_tx dut (
        .clk(clk),
        .rst_n(rst_n),
        .tx_en(tx_en),
        .tx_data(tx_data),
        .lvds_out(lvds_out)
    );
    
    // 生成125MHz系统时钟
    initial begin
        clk = 0;
        forever #(CLK_PERIOD/2) clk = ~clk;
    end
    
    // 监控LVDS输出
    initial begin
        $display("start lvds testing...");
        $display("sysclk: 125MHz, output clk: 100MHz");
        
        // 记录测试过程到文件
        $dumpfile("lvds_tx_test.vcd");
        $dumpvars(0, lvds_tb);
    end

    // 简单的接收机模型用于验证传输
    reg prev_clock;
    initial begin
        bit_index = 0;
        error_count = 0;
        received_data = 64'h0;
        prev_clock = 0;
        
        forever begin
            @(posedge clk);
            
            // 检测随路时钟上升沿
            if (lvds_out[0] && !prev_clock) begin
                // 在随路时钟上升沿采样数据位
                if (tx_en && bit_index < 64) begin
                    received_data[bit_index]   = lvds_out[1];
                    
                    if (bit_index+1 < 64)
                        received_data[bit_index+1] = lvds_out[2];
                    
                    if (bit_index+2 < 64)
                        received_data[bit_index+2] = lvds_out[3];
                    
                    bit_index = bit_index + 3;
                end
            end
            
            prev_clock = lvds_out[0];
        end
    end
    
    // 验证接收的数据
    task verify_data;
        input [63:0] expected;
        begin
            wait (bit_index >= 64);
            
            if (received_data !== expected) begin
                $display("error: unexpected data received");
                $display("expected: %h", expected);
                $display("received: %h", received_data);
                error_count = error_count + 1;
            end
            else begin
                $display("success: data matched %h", received_data);
            end
            
            // 重置接收器以准备下一次传输
            bit_index = 0;
            received_data = 64'h0;
        end
    endtask

    // 测试主程序
    initial begin
        // 初始化输入信号
        rst_n = 0;
        tx_en = 0;
        tx_data = 64'h0;
        
        // 复位过程
        #20;
        rst_n = 1;
        #20;
        
        // 测试用例1: 发送固定模式
        $display("\ntest case 1: send fixed pattern data");
        tx_data = 64'ha123456789ABCDEF;
        
        // 等待一会，确保前面的复位完全生效
        #100;
        
        // 启用发送
        @(posedge clk);
        tx_en = 1;

        // 等待足够长的时间让数据传输完成
        #1000;
        
        // 验证数据
        verify_data(64'ha123456789ABCDEF);
        
        // 禁用发送，等待一段时间
        @(posedge clk);
        tx_en = 0;
        #200;
        
        // 测试用例2: 发送交替模式
        $display("\ntest case 2: ");
        tx_data = 64'hAAAA5555AAAA5555;
        
        // 启用发送
        @(posedge clk);
        tx_en = 1;
        
        // 等待足够长的时间让数据传输完成
        #1000;
        
        // 验证数据
        verify_data(64'hAAAA5555AAAA5555);
        
        // 禁用发送
        @(posedge clk);
        tx_en = 0;
        #200;
        
        // 测试用例3: 连续传输两个数据包
        $display("\ntest case 3: ");
        tx_data = 64'ha111222233334444;
        
        // 启用发送
        @(posedge clk);
        tx_en = 1;
        
        // 等待数据传输开始后，发送第一个数据包的过程中更新数据
        #100;
        tx_data = 64'h5555666677778888;
        
        // 等待足够长的时间让数据传输完成
        #1000;
        
        // 验证第二个数据包
        verify_data(64'ha111222233334444);
        
        // 禁用发送
        @(posedge clk);
        tx_en = 0;
        #200;
        
        // 报告测试结果
        $display("\n-------------------------------------");
        if (error_count == 0) begin
            $display("test complete: all tests passed");
        end else begin
            $display("test complete:  %d error", error_count);
        end
        $display("-------------------------------------\n");
        
        // 结束仿真
        #100 $finish;
    end
    
endmodule 