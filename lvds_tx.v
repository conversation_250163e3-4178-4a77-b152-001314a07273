module lvds_tx(
    input               clk,              // 系统时钟 125MHz
    input               rst_n,            // 异步复位，低电平有效
    input               tx_en,            // 发送使能，高电平有效
    input       [63:0]  tx_data,          // 输入数据，8个字节
    output reg          tx_done,         // 发送完成标志
    output reg  [3:0]   lvds_out          // LVDS输出，包含随路时钟和数据
);

// 参数定义
// localparam SYS_CLK_FREQ = 125_000_000;     // 系统时钟频率 125MHz
// localparam OUT_CLK_FREQ = 100_000_000;      // 输出随路时钟频率 100MHz
// 使用相位累加器实现精确分频 (100MHz = 125MHz * 4/5)
// localparam PHASE_INC = 32'd3435973836;      // 2^32 * 0.2 (100/125 = 4/5, 所以每周期前进0.8，相当于每次减少0.2)
// localparam BYTE_WIDTH = 8;                  // 每个字节的位宽

// 内部信号
// reg [31:0] phase_acc;                      // 相位累加器
// reg        out_clk;                         // 输出随路时钟
// reg [63:0] tx_data_reg;                    // 输入数据寄存器
// reg [5:0]  bit_cnt;                        // 位计数器
// reg        tx_active;                      // 传输激活标志
// reg [1:0]  data_sel;                       // 数据选择器
// reg [2:0]  data_out_reg[0:2];              // 数据输出寄存器
// reg        prev_phase_msb;                 // 上一个时钟周期的相位MSB
// reg        lvds_clock_enabled;             // 控制随路时钟的输出使能

// reg [2:0]  data_out_reg1;
// reg [2:0]  data_out_reg2;
// reg [2:0]  data_out_reg3;

// reg [31:0] phase_threshold;
reg [3:0] cnt;
reg lvds_clk;
reg [63:0] tx_reg;
// reg [3:0] data_out;
reg [6:0] lvds_bit;

reg tx_en_d0;
// reg tx_en_d1;
// reg tx_en_d2;
reg tx_en_flag;
// wire tx_en_valid;

assign tx_en_rise = tx_en&(~tx_en_d0);
always @(posedge clk or negedge rst_n) begin
    if(!rst_n)begin
        tx_en_d0 <= 1'b0;
        // tx_en_d1 <= 1'b0;
        // tx_en_d2 <= 1'b0;
    end
    else begin
        tx_en_d0 <= tx_en;
        // tx_en_d1 <= tx_en_d0;
        // tx_en_d2 <= tx_en_d1;
    end
end

always @(posedge clk or negedge rst_n) begin
    if(!rst_n)begin
        tx_en_flag <= 1'b0;
        tx_done <= 1'b1;
    end
    else if(tx_en_rise)begin
        tx_en_flag <= 1'b1;
        tx_done <= 1'b0;
    end else if(lvds_bit > 7'd64)begin
        tx_en_flag <= 1'b0;
        tx_done <= 1'b1;
    end
end

always@(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        cnt <= 4'd0;
    end
    else begin
        if(tx_en_flag)begin
            cnt <= cnt + 4'd1;
            if(cnt == 4'd4)begin
                cnt <= 4'd0;
            end
        end
        else begin
            cnt <= 4'd0;
        end
    end
end

always @(posedge clk or negedge rst_n) begin
    if(!rst_n)begin
        lvds_clk <= 0;
    end
    else begin
        if(cnt == 4'd2 )begin
            lvds_clk <= 1'b1;
        end else if(cnt == 4'd4)begin
            lvds_clk <= 1'b0;
        end else if(!tx_en_flag)
            lvds_clk <= 1'b0;
    end
end

always @(posedge clk or negedge rst_n) begin
    if(!rst_n)begin
        tx_reg <= 64'd0;
    end
    else begin
        if(tx_en_rise)begin
            tx_reg <= tx_data;
        end else begin
            tx_reg <= tx_reg;
        end
    end
end

always @(posedge clk or negedge rst_n) begin
    if(!rst_n)begin
        // data_out <= 4'b0000;
        lvds_bit <= 7'd0;
        lvds_out <= 4'b0000;
    end
    else begin
        lvds_out[0] <= lvds_clk;
        if(lvds_bit > 7'd64)begin
            lvds_bit <= 7'd0;
        end else if(cnt == 4'd1)begin
            lvds_bit <= lvds_bit + 7'd3;
            if(lvds_bit == 7'd63)begin
                lvds_out[1] <= tx_reg[lvds_bit];
                lvds_out[2] <= 0;
                lvds_out[3] <= 0;
                // lvds_bit <= 7'd0;
            end else begin
                lvds_out[1] <= tx_reg[lvds_bit];
                lvds_out[2] <= tx_reg[lvds_bit+1];
                lvds_out[3] <= tx_reg[lvds_bit+2];
                // lvds_bit <= lvds_bit + 7'd3;            
            end
        end


    end
end

// 使用相位累加器产生100MHz输出时钟
// always @(posedge clk or negedge rst_n) begin
//     if (!rst_n) begin
//         phase_acc <= 32'd0;
//         prev_phase_msb <= 1'b0;
//         phase_threshold <= 32'h80000000;
//         out_clk <= 1'b0;
//         lvds_clock_enabled <= 1'b0;
//     end
//     else begin
//         // 相位累加器：精确控制时钟频率
//         // 100MHz = 125MHz * 4/5，所以相位累加器每个周期前进0.8
//         phase_acc <= phase_acc + PHASE_INC; // 减少PHASE_INC相当于增加0.8
        
//         // 检测MSB的翻转，产生时钟沿
//         prev_phase_msb <= phase_acc[31];
        
//         // 启用/禁用随路时钟输出
//         if (tx_en && !lvds_clock_enabled) begin
//             // 开始传输，等待下一个周期开始生成时钟
//             lvds_clock_enabled <= 1'b1;
//             out_clk <= 1'b0; // 确保从低电平开始
//         end
//         else if (!tx_en && lvds_clock_enabled && !out_clk) begin
//             // 当传输结束，且时钟处于低电平时，停止时钟
//             lvds_clock_enabled <= 1'b0;
//         end
        
//         // 只有在时钟使能时才产生时钟沿
//         if (lvds_clock_enabled) begin
//             //当MSB从0变为1时产生上升沿，从1变为0时产生下降沿
//             if (!phase_acc[31] && prev_phase_msb) begin
//                 out_clk <= 1'b1; // 上升沿
//             end
//             else if (phase_acc[31] && !prev_phase_msb) begin
//                 out_clk <= 1'b0; // 下降沿
//             end
//         end
//         else begin
//             out_clk <= 1'b0; // 未使能时保持低电平
//         end
//     end
// end

// // 数据发送控制逻辑
// always @(posedge clk or negedge rst_n) begin
//     if (!rst_n) begin
//         tx_active <= 1'b0;
//         tx_data_reg <= 64'd0;
//         bit_cnt <= 6'd0;
//         data_sel <= 2'd0;
//     end
//     else begin
//         // 检测发送使能上升沿并且时钟已经准备好时才开始传输
//         if (tx_en && !tx_active && lvds_clock_enabled) begin
//             // 在第一个时钟上升沿开始发送，需要提前准备数据
//             if (!phase_acc[31] && prev_phase_msb) begin
//                 tx_active <= 1'b1;
//                 tx_data_reg <= tx_data;  // 锁存输入数据
//                 bit_cnt <= 6'd0;
//                 data_sel <= 2'd0;
//             end
//         end
//         // 数据传输过程
//         else if (tx_active) begin
//             // 在时钟边沿更新数据
//             if ((!phase_acc[31] && prev_phase_msb) || (phase_acc[31] && !prev_phase_msb)) begin
//                 // 发送完64位数据后，结束传输或继续下一组
//                 if (bit_cnt >= 6'd63) begin
//                     // 如果使能仍然有效，继续新的传输
//                     if (tx_en) begin
//                         tx_data_reg <= tx_data;  // 获取新数据
//                         bit_cnt <= 6'd0;
//                     end
//                     else begin
//                         tx_active <= 1'b0;  // 结束传输
//                     end
//                 end
//                 // else if(data_sel ==2'd2)begin
//                 //     bit_cnt <= bit_cnt + 6'd9;  // 增加位计数器
//                 // end
                
//                 // 更新数据选择器
//                 if(data_sel == 2'd2)begin
//                     data_sel <= 2'd0;
//                     bit_cnt <= bit_cnt + 6'd9;
//                 end else begin
//                     data_sel <= data_sel + 1'b1; // 每2位循环一次
//                 end
//             end
//         end
        
//         // 如果发送使能被撤销，在当前数据包传输完成后结束
//         if (!tx_en && bit_cnt >= 6'd63) begin
//             tx_active <= 1'b0;
//         end
//     end
// end

// // 数据预处理 - 在时钟边沿之前准备好数据
// always @(posedge clk or negedge rst_n) begin
//     if (!rst_n) begin
//         data_out_reg[0] <= 3'b000;
//         data_out_reg[1] <= 3'b000;
//         data_out_reg[2] <= 3'b000;
//         data_out_reg[3] <= 3'b000;
//         data_out_reg1 <= 3'b000;
//         data_out_reg2 <= 3'b000;
//         data_out_reg3 <= 3'b000;
//     end
//     else if (tx_active) begin
//         // 每个data_sel对应发送3个bit
//         // 数据组0 (0,1,2)
//         // data_out_reg[0][0] <= (bit_cnt < 6'd64) ? tx_data_reg[bit_cnt] : 1'b0;
//         // data_out_reg[0][1] <= (bit_cnt+1 < 6'd64) ? tx_data_reg[bit_cnt+1] : 1'b0;
//         // data_out_reg[0][2] <= (bit_cnt+2 < 6'd64) ? tx_data_reg[bit_cnt+2] : 1'b0;
//         // data_out_reg1[0] <= (bit_cnt < 6'd64) ? tx_data_reg[bit_cnt] : 1'b0;
//         if(bit_cnt < 6'd64)begin
//             data_out_reg1[0] <= tx_data_reg[bit_cnt];
//         end
//         else begin
//             data_out_reg1[0] <= 1'b0;
//         end
//         if(bit_cnt < 6'd63)begin
//             data_out_reg1[1] <= tx_data_reg[bit_cnt+1];
//         end
//         else begin
//             data_out_reg1[1] <= 1'b0;
//         end
//         if(bit_cnt < 6'd62)begin
//             data_out_reg1[2] <= tx_data_reg[bit_cnt+2];
//         end
//         else begin
//             data_out_reg1[2] <= 1'b0;  
//         end
//         // data_out_reg1[1] <= (bit_cnt+1 < 6'd64) ? tx_data_reg[bit_cnt+1] : 1'b0;
//         // data_out_reg1[2] <= (bit_cnt+2 < 6'd64) ? tx_data_reg[bit_cnt+2] : 1'b0;
        
//         // 数据组1 (3,4,5)
//         // data_out_reg[1][0] <= (bit_cnt+3 < 6'd64) ? tx_data_reg[bit_cnt+3] : 1'b0;
//         // data_out_reg[1][1] <= (bit_cnt+4 < 6'd64) ? tx_data_reg[bit_cnt+4] : 1'b0;
//         // data_out_reg[1][2] <= (bit_cnt+5 < 6'd64) ? tx_data_reg[bit_cnt+5] : 1'b0;
//         data_out_reg2[0] <= (bit_cnt+3 < 6'd64) ? tx_data_reg[bit_cnt+3] : 1'b0;
//         data_out_reg2[1] <= (bit_cnt+4 < 6'd64) ? tx_data_reg[bit_cnt+4] : 1'b0;
//         data_out_reg2[2] <= (bit_cnt+5 < 6'd64) ? tx_data_reg[bit_cnt+5] : 1'b0;
        
//         // 数据组2 (6,7,8)
//         // data_out_reg[2][0] <= (bit_cnt+6 < 6'd64) ? tx_data_reg[bit_cnt+6] : 1'b0;
//         // data_out_reg[2][1] <= (bit_cnt+7 < 6'd64) ? tx_data_reg[bit_cnt+7] : 1'b0;
//         // data_out_reg[2][2] <= (bit_cnt+8 < 6'd64) ? tx_data_reg[bit_cnt+8] : 1'b0;
//         data_out_reg3[0] <= (bit_cnt+6 < 6'd64) ? tx_data_reg[bit_cnt+6] : 1'b0;
//         data_out_reg3[1] <= (bit_cnt+7 < 6'd64) ? tx_data_reg[bit_cnt+7] : 1'b0;
//         data_out_reg3[2] <= (bit_cnt+8 < 6'd64) ? tx_data_reg[bit_cnt+8] : 1'b0;


//     end
//     else begin
//         data_out_reg[0] <= 3'b000;
//         data_out_reg[1] <= 3'b000;
//         data_out_reg[2] <= 3'b000;
//         data_out_reg[3] <= 3'b000;
//         data_out_reg1 <= 3'b000;
//         data_out_reg2 <= 3'b000;
//         data_out_reg3 <= 3'b000;
//     end
// end

// LVDS输出控制 - 将数据与时钟正确同步
// always @(posedge clk or negedge rst_n) begin
//     if (!rst_n) begin
//         lvds_out <= 4'b0000;  // 复位时所有位都为低电平
//     end
//     else begin
//         // 随路时钟从LVDS的第0位输出，仅在传输期间有效
//         lvds_out[0] <= out_clk;
        
//         // 数据位输出 - 确保数据在时钟上升沿稳定
//         if (tx_active) begin
//             // 根据data_sel选择正确的数据组
//             // lvds_out[3:1] <= data_out_reg[data_sel];
//             case (data_sel)
//                 2'd0: lvds_out[3:1] <= data_out_reg1;
//                 2'd1: lvds_out[3:1] <= data_out_reg2;
//                 2'd2: lvds_out[3:1] <= data_out_reg3;
//                 default: lvds_out[3:1] <= lvds_out[3:1];
//             endcase
//         end
//         else begin
//             lvds_out[3:1] <= 3'b000;  // 非传输状态，数据位为0
//         end
//     end
// end

endmodule 