module lvds_tx_ctrl (
    input               clk,            // 系统时钟
    input               rst_n,          // 复位信号，低电平有效
    input               tx_en,          // 外部发送使能
    input               tx_done,        // LVDS发送完成信号
    input               fifo_empty,     // FIFO空标志
    input wire [31:0]   pulse_count,    // 脉冲数量
    input wire [31:0]   trigger_time,   // 每一行的执行时间
    input wire [31:0]   udp_pac_num,       //上位机下发的包号
    input               device_start,     // 设备启动信号
    output wire         fifo_rd_rst,     // FIFO读使能，ram是低电平有效
    // output reg   [7:0]  fifo_addr,     // FIFO读地址
    output wire  [9:0]  addr,            // FIFO读地址,乒乓地址
    output reg          lvds_tx_en      // LVDS发送使能
);

// 参数定义
parameter CLK_FREQ = 125_000_000;    // 时钟频率125MHz
parameter US_CYCLES = CLK_FREQ/1000_000; // 1us所需时钟周期数
parameter TRIGGER_TIME_DEFAULT = 800;    // 默认触发时间800us
// 状态定义
localparam IDLE        = 2'b00;   // 空闲状态
localparam PRE_READ    = 2'b01;   // 预读状态
localparam WAIT_DONE   = 2'b10;   // 等待发送完成状态
localparam RESTART = 2'b11;   // 重新初始化状态
// localparam CHECK_COUNT = 2'b11;   // 检查计数状态
reg fifo_rd_en;
assign fifo_rd_rst = ~fifo_rd_en;

reg  [8:0] read_ptr;
reg  [6:0] fifo_addr;
reg [1:0] current_state, next_state;
reg [6:0] byte_count;  // 字节计数器，最大需要计数到125
reg [31:0] delay_count;  
reg [31:0] trigger_time_buf;
reg tx_en_d1;
reg tx_done_d1;
reg device_start_d1;
reg start_flag;
wire tx_en_posedge;
wire tx_done_negedge;
wire device_start_posedge;
// 检测tx_en上升沿
assign tx_en_posedge = tx_en & ~tx_en_d1;
assign tx_done_negedge = !tx_done & tx_done_d1;
assign device_start_posedge = device_start & ~device_start_d1;

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        start_flag <= 1'b0;
    end
    else if(device_start_posedge) begin
        start_flag <= 1'b1;
    end
end

// 边沿检测
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        tx_en_d1 <= 1'b0;
        tx_done_d1 <= 1'b0;
        device_start_d1 <= 1'b0;
    end
    else begin
        tx_en_d1 <= tx_en;
        tx_done_d1 <= tx_done;
        device_start_d1 <= device_start;
    end
end

// 状态寄存器
always @(posedge clk or negedge rst_n) begin
    if (!rst_n)
        current_state <= IDLE;
    else
        current_state <= next_state;
end


always @(posedge clk or negedge rst_n) begin
    if (!rst_n)begin
        trigger_time_buf <= TRIGGER_TIME_DEFAULT * US_CYCLES;
    end
    else if(trigger_time != 32'd0) begin
        trigger_time_buf <= trigger_time * US_CYCLES;
    end
end


// 延时计数器
always @(posedge clk or negedge rst_n) begin
    if (!rst_n)
        delay_count <= 32'd0;
    else if (current_state == IDLE)
        delay_count <= 32'd0;
    else if (delay_count == trigger_time_buf)
        delay_count <= 32'd0;
    else
        delay_count <= delay_count + 32'd1;  
end

// 状态转换逻辑
always @(*) begin
    case (current_state)
        IDLE: begin
            if (tx_en_posedge && start_flag)
                next_state = PRE_READ;
            else
                next_state = IDLE;
        end

        PRE_READ: begin
                next_state = WAIT_DONE;
        end
        
        WAIT_DONE: begin
            if (byte_count == 7'd125)
                next_state = IDLE;
            else if (tx_en_posedge ) 
                next_state = RESTART;  // 转到重新初始化状态
            else if (delay_count == trigger_time_buf)
                next_state = PRE_READ;
            else
                next_state = WAIT_DONE;
        end

        RESTART: begin
            next_state = PRE_READ;  // 立即进入PRE_READ状态
        end        
        // CHECK_COUNT: begin
        //     if (byte_count >= 7'd125)  // 已发送125字节
        //         next_state = IDLE;
        //     else if (!fifo_empty)
        //         next_state = WAIT_DONE;
        //     else
        //         next_state = CHECK_COUNT;
        // end
        
        default: next_state = IDLE;
    endcase
end

// 输出逻辑
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        fifo_rd_en <= 1'b0;
        lvds_tx_en <= 1'b0;
        byte_count <= 7'd0;
        fifo_addr <=  7'd0;
        read_ptr <=  9'd0;
    end
    else begin
        fifo_rd_en <= 1'b0;
        lvds_tx_en <= 1'b0;
        case (current_state)
            IDLE: begin
                if (tx_en_posedge && (pulse_count <= udp_pac_num) && start_flag) begin//pulse_count是延时之后计数脉冲
                    fifo_rd_en <= 1'b1;
                    // lvds_tx_en <= 1'b1;
                end
                if(tx_en_posedge  && start_flag) begin
                    if(read_ptr == 9'd500)begin
                        read_ptr <= 9'd0;
                    end else if (pulse_count == 32'd1)begin // 第一个脉冲从地址0开始
                        read_ptr <= 9'd0;
                    end else begin
                        read_ptr <= read_ptr + 9'd125;
                    end
                end
                byte_count <= 7'd0;
                fifo_addr <= 7'd0;
            end

            PRE_READ: begin
                if(pulse_count <= udp_pac_num)begin
                    fifo_rd_en <= 1'b1;
                    lvds_tx_en <= 1'b1;
                end
            end
            
            WAIT_DONE: begin
                if(byte_count == 7'd125)
                    fifo_rd_en <= 1'b0;
                else if(pulse_count <= udp_pac_num)//有可能出现读写地址相同?
                    fifo_rd_en <= 1'b1;
                if (delay_count == trigger_time_buf - 32'd500) begin
                    fifo_addr <= fifo_addr + 7'd1;
                    byte_count <= byte_count + 7'd1;
                end
            end

            RESTART: begin
                // 执行IDLE状态中tx_en_posedge触发的逻辑
                fifo_rd_en <= 1'b1;
                // 更新read_ptr逻辑
                if(read_ptr == 9'd500)
                    read_ptr <= 9'd0;
                else if (pulse_count == 32'd1)
                    read_ptr <= 9'd0;
                else
                    read_ptr <= read_ptr + 9'd125;
                // 重置计数器
                byte_count <= 7'd0;
                fifo_addr <= 7'd0;
            end
            // CHECK_COUNT: begin
            //     if (!fifo_empty && byte_count < 7'd126) begin
            //         fifo_rd_en <= 1'b1;
            //         lvds_tx_en <= 1'b1;
            //     end
            // end
            
            default: begin

            end
        endcase
    end
end
assign  addr = read_ptr + fifo_addr;
endmodule
