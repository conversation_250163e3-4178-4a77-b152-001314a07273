`timescale 1ns/1ps

module lvds_tx_ctrl_tb();

// 时钟周期参数
localparam CLK_PERIOD = 8;  // 125MHz时钟

// 信号定义
reg         clk;
reg         rst_n;
reg         tx_en;
reg         tx_done;
reg         fifo_empty;
reg  [63:0] fifo_data;
wire        fifo_rd_en;
wire        lvds_tx_en;

// 时钟生成
initial begin
    clk = 0;
    forever #(CLK_PERIOD/2) clk = ~clk;
end

// 被测模块例化
lvds_tx_ctrl u_lvds_tx_ctrl(
    .clk        (clk),
    .rst_n      (rst_n),
    .tx_en      (tx_en),
    .tx_done    (tx_done),
    .fifo_empty (fifo_empty),
    .fifo_rd_en (fifo_rd_en),
    .lvds_tx_en (lvds_tx_en)
);

// 测试向量
initial begin
    // 初始化
    rst_n = 0;
    tx_en = 0;
    tx_done = 1;
    fifo_empty = 1;
    fifo_data = 64'h0;
    
    // 等待5个时钟周期后释放复位
    #(CLK_PERIOD*5);
    rst_n = 1;
    #(CLK_PERIOD*5);
    
    // 测试场景1：正常发送过程
    fifo_empty = 0;  // FIFO中有数据
    @(posedge clk);
    tx_en = 1;       // 触发发送
    
    // 模拟125组数据的发送过程
    repeat(125) begin
        // 等待FIFO读使能
        @(posedge fifo_rd_en);
        // 更新FIFO数据
        fifo_data = fifo_data + 64'h1;
        tx_done = 0;
        // 等待几个时钟周期模拟发送延迟
        repeat(5) @(posedge clk);
        // 设置发送完成标志
        
        @(posedge clk);
        tx_done = 1;
        @(posedge clk);
    end
    
    // // 测试场景2：FIFO空的情况
    // #(CLK_PERIOD*10);
    // fifo_empty = 1;
    // tx_en = 0;
    // #(CLK_PERIOD*10);
    // tx_en = 1;
    // #(CLK_PERIOD*10);
    
    // // 测试场景3：发送过程中的异常复位
    // fifo_empty = 0;
    // #(CLK_PERIOD*5);
    // rst_n = 0;
    // #(CLK_PERIOD*5);
    // rst_n = 1;
    
    // // 等待一段时间后结束仿真
    // #(CLK_PERIOD*100);
    $finish;
end

// // 监控关键信号
// initial begin
//     $monitor("Time=%0t rst_n=%b tx_en=%b tx_done=%b fifo_empty=%b fifo_rd_en=%b lvds_tx_en=%b", 
//              $time, rst_n, tx_en, tx_done, fifo_empty, fifo_rd_en, lvds_tx_en);
// end

// 检查点：验证计数和控制信号
// reg [6:0] transfer_count;
// always @(posedge clk) begin
//     if (!rst_n)
//         transfer_count <= 0;
//     else if (fifo_rd_en && !fifo_empty)
//         transfer_count <= transfer_count + 1;
        
//     // 验证发送次数不超过16次（128字节）
//     if (transfer_count > 7'd125)
//         $display("Error: Transfer count exceeded 16!");
// end

endmodule
