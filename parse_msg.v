module parse_msg (
    input               clk,            // 系统时钟
    input               rst_n,          // 异步复位（低有效）
    input               rx_done,        // 接收完成信号（FIFO非空）
    input [15:0]        total_bytes,    // 接收到的总字节数
    input [7:0]         fifo_data,      // FIFO输出数据（单字节）

    output reg          fifo_rd_en,     // FIFO读使能
    // 解析后的控制信号
    output reg          device_enable,  // 设备使能（1:启动, 0:停止）
    output reg  [31:0]  blow_delay_x,   // 喷吹延时X（单位：800us）
    output reg  [31:0]  line_trigger,   // 行触发时间（us）
    output reg  [31:0]  total_packets,  // 数据总包数（1-7）
    output reg          parser_valid,   // 解析完成有效信号
    output reg          error_flag      // 错误标志（包类型/长度错误）
);
    
reg [31:0]   fpga_trigger_time;
reg [31:0]   trigger_method;
reg [31:0]   duty_cycle;
reg [7:0]    bag_type;
// reg          parser_valid;
// reg          error_flag;
reg          rx_done_d0;
reg          rx_done_d1;
reg          rx_done_d2;

reg [7:0] mydata[0:31];

reg [4:0] cur_state,next_state;
localparam  IDLE                    = 5'b0_0001; //
localparam  FIFO_PREPARE            = 5'b0_0010; //
localparam  PARSE_DATA              = 5'b0_0100; //
localparam  VALIDATE                = 5'b0_1000;
localparam  ERROR                   = 5'b1_0000;

reg [15:0]  byte_cnt;           // 字段内字节计数器（0-3）
reg [15:0]  processed_length;    // 已处理的字节数
reg [15:0]  bytes_tobe_parsed;// 需要解析的字节数

reg [31:0]  temp_data;

localparam MIN_PACKET_LENGTH = 32;  // 协议要求8个字段*4字节=32字节

wire rx_done_valid;
assign rx_done_valid = (~rx_done_d2) & rx_done_d1;
always @(posedge clk or negedge rst_n) begin
    if(!rst_n)begin
        rx_done_d0 <= 1'b0;
        rx_done_d1 <= 1'b0;
        rx_done_d2 <= 1'b0;
    end
    else begin
        rx_done_d0 <= rx_done;
        rx_done_d1 <= rx_done_d0;
        rx_done_d2 <= rx_done_d1;
    end
end


always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        bytes_tobe_parsed <= 16'd0;
    end else if (rx_done_valid) begin
        bytes_tobe_parsed <= total_bytes;
    end 
end

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        cur_state <= IDLE;
    end else begin
        cur_state <= next_state;
    end
end

always @(*) begin
    next_state = cur_state;
    case (cur_state)
        IDLE: begin
            if (rx_done_valid) begin
                if (total_bytes < MIN_PACKET_LENGTH) begin
                    next_state = ERROR;  // 包长度不足
                end else begin
                    next_state = FIFO_PREPARE;
                end
            end
        end

        FIFO_PREPARE: begin
            if (byte_cnt == 4) begin
                next_state = PARSE_DATA;
            end
        end       

        PARSE_DATA: begin
            if (byte_cnt == bytes_tobe_parsed) next_state = VALIDATE;
        end

        VALIDATE: begin
            next_state = (total_packets < 1 || total_packets > 7) ? ERROR : IDLE;
        end

        ERROR: next_state = IDLE;
        default: next_state = IDLE;         
    endcase
end

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        // 寄存器输出复位
        fifo_rd_en    <= 0;
        device_enable <= 0;
        blow_delay_x  <= 0;
        line_trigger  <= 0;
        total_packets <= 0;
        fpga_trigger_time <= 0;
        trigger_method <= 0;
        processed_length <= 16'd0;
        parser_valid  <= 0;
        error_flag    <= 0;
        byte_cnt      <= 0;
    end else begin
        // 默认值
        fifo_rd_en <= 0;
        parser_valid <= 0;
        error_flag <= 0;

        case (cur_state)
            IDLE: begin
                byte_cnt <= 0;
                processed_length <= 16'd0;
            end

            FIFO_PREPARE: begin
                if(byte_cnt < 4'd4)begin
                    byte_cnt <= 4'd4;
                    fifo_rd_en <= 1;
                end else begin
                    byte_cnt <= 4'd0;
                    fifo_rd_en <= 1;
                end
            end

            PARSE_DATA: begin
                if(byte_cnt < bytes_tobe_parsed)begin
                    mydata[byte_cnt] <= fifo_data;
                    temp_data <= {temp_data[23:0],fifo_data};
                    if(byte_cnt == 15'd7)begin
                        device_enable <= fifo_data;
                    end
                    if(byte_cnt == 15'd12)begin
                        blow_delay_x <= temp_data;
                    end
                    byte_cnt  <= byte_cnt + 16'd1;
                    fifo_rd_en <= 1;
                end else begin
                    byte_cnt <= 0;
                    bag_type <= mydata[3];
                    // device_enable <= mydata[7];
                    // blow_delay_x <= {mydata[8],mydata[9],mydata[10],mydata[11]};
                    line_trigger  <= {mydata[12],mydata[13],mydata[14],mydata[15]};
                    total_packets <= {mydata[16],mydata[17],mydata[18],mydata[19]};
                    fpga_trigger_time <= {mydata[20],mydata[21],mydata[22],mydata[23]};
                    trigger_method <= {mydata[24],mydata[25],mydata[26],mydata[27]};
                    duty_cycle <= {mydata[28],mydata[29],mydata[30],mydata[31]};
                    fifo_rd_en <= 0;
                end
            end

            VALIDATE: begin
                if (total_packets >=1 && total_packets <=7) begin
                    parser_valid <= 1;
                end
            end

            ERROR: begin
                error_flag <= 1;
            end
        endcase
    end
end

endmodule