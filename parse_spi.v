// File: parse_spi.v
module parse_spi (
    input   wire     clk,
    input   wire     rst_n,
    input   wire     transmit_done,   // SPI传输完成信号
    input  wire [7:0] rec_byte,       // 需解析的字节数
    input  wire [7:0] rd_data,         // FIFO读取数据
    input  wire      rd_empty,        // FIFO空标志
    output       rd_en,           // FIFO读使能
    output reg [7:0] data_out,       // 解析后的数据输出
    output       parsing          // 解析状态标志
);


parameter IDLE    = 2'd0;
parameter READING = 2'd1;
parameter PARSING = 2'd2;

reg [1:0] current_state;
reg [1:0] next_state;
reg [7:0] byte_counter;

// 信号声明
reg rd_en_reg;
reg parsing_reg;

// 状态转移组合逻辑
always @(*) begin
    next_state = current_state;
    case(current_state)
        IDLE: begin
            if(transmit_done) begin
                next_state = READING;
            end
        end
        
        READING: begin
            if(!rd_empty && byte_counter == rec_byte - 1) begin
                next_state = PARSING;
            end
        end
        
        PARSING: begin
            next_state = IDLE;
        end
        
        default: begin
            next_state = IDLE;
        end
    endcase
end

// 寄存器更新时序逻辑
always @(posedge clk or negedge rst_n) begin
    if(!rst_n) begin
        current_state <= IDLE;
    end else begin
        current_state <= next_state;
    end
end

// 输出和计数器时序逻辑
always @(posedge clk or negedge rst_n) begin
    if(!rst_n) begin
        rd_en_reg <= 0;
        parsing_reg <= 0;
        byte_counter <= 0;
        data_out <= 8'd0;
    end else begin
        case(current_state)
            IDLE: begin
                parsing_reg <= 0;
                if(transmit_done) begin
                    rd_en_reg <= 1;
                    byte_counter <= 0;
                end
            end
            
            READING: begin
                if(!rd_empty && byte_counter < rec_byte) begin
                    if(byte_counter == rec_byte - 1) begin
                        rd_en_reg <= 0;
                    end
                    byte_counter <= byte_counter + 1;
                end
                data_out <= rd_data + 8'd1; // 输出读取的数据
            end
            
            PARSING: begin
                parsing_reg <= 1;
            end
            
            default: begin
                rd_en_reg <= 0;
                parsing_reg <= 0;
                byte_counter <= 0;
            end
        endcase
    end
end

// 输出赋值
assign rd_en = rd_en_reg;
assign parsing = parsing_reg;

endmodule