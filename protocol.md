UDP 通信协议 V9

PC 的 IP 需要******************，板子的 IP 是******************，端口号
9020, MAC是00-0a-35-01-fe-c0，速度1G，需要PC静态绑定

通讯控制板每秒会向计算机发送状态信息(也作为在线连接状态指示)：

  ----------------------------------------------------------------------------------------
  字节序数     字节大小   含义                值说明
  ------------ ---------- ------------------- --------------------------------------------
  0x01\~0x04   4字节      包的种类            0x58表示状态数据包（表示通讯在线）

  0x05\~0x08   4字节      喷吹延时X           喷吹执行延时，X\*800 us

  0x09\~0x0C   4字节      行触发时间(us)      喷吹执行时间us 800

  0x0D\~0x10   4字节      接收触发信号总数    返回接收触发信号总数

  0x11\~0x14   4字节      丢失触发信号总数A   返回丢失触发信号总数，低于90ms

  0x15\~0x18   4字节      接收数据包总数      返回接收数据包总数（根据总包数计算）

  0x19\~0x1C   4字节      丢失数据包总数A     丢失接收数据包总数（根据总包数计算）（少）

  0x1D\~0x20   4字节      阀信号执行频率/秒   返回每秒所有阀的开启次数

  0x21\~0x24   4字节      阀执行总次数        返回设备通电后所有阀的工作次数

  0x25\~0x18   4字节      丢失触发信号总数B   返回丢失触发信号总数，高于110ms

  0x29\~0x2C   4字节      丢失数据包总数B     丢失接收数据包总数（根据总包数计算）（多）

  0x2D\~0x30   4字节      待定                待定
  ----------------------------------------------------------------------------------------

UDP 通信中有下面两个包的种类

1：采集指令控制包。

+-----------+---------+--------------+-------------------------------+
| 字节序数  | 字      | 含义         | 值说明                        |
|           | 节大小  |              |                               |
+:==========+:========+:=============+:==============================+
| 0         | 4字节   | 包的种类     | 0 表示采集控制包              |
| x01\~0x04 |         |              |                               |
+-----------+---------+--------------+-------------------------------+
| 0         | 4字节   | 设备开始     | 1表示设备开始(使能置1)        |
| x05\~0x08 |         |              |                               |
|           |         | 设备停止     | 0 表示设备停止(使能置0)       |
+-----------+---------+--------------+-------------------------------+
| 0         | 4字节   | 喷吹延时X    | 喷吹执行延时，X\*800 us       |
| x09\~0x0C |         |              |                               |
+-----------+---------+--------------+-------------------------------+
| 0         | 4字节   | 行           | 喷吹执行时间us 800            |
| x0D\~0x10 |         | 触发时间(us) |                               |
+-----------+---------+--------------+-------------------------------+
| 0         | 4字节   | 数据总包数   | 阀位数1-80 : 1个数据包        |
| x11\~0x14 |         |              |                               |
|           |         |              | 阀位数81-160 : 2个数据包      |
|           |         |              |                               |
|           |         |              | 阀位数161-240 : 3个数据包     |
|           |         |              |                               |
|           |         |              | 阀位数241-320 : 4个数据包     |
|           |         |              |                               |
|           |         |              | 阀位数321-400 : 5个数据包     |
|           |         |              |                               |
|           |         |              | 阀位数401-480 : 6个数据包     |
|           |         |              |                               |
|           |         |              | 阀位数481-560 : 7个数据包     |
+-----------+---------+--------------+-------------------------------+
| 0         | 4字节   | FPGA         | FPGA向外触发的时间(暂未使用)  |
| x15\~0x18 |         | 触发时间(us) |                               |
+-----------+---------+--------------+-------------------------------+
| 0         | 4字节   | FPGA触发方式 | 1：外触发 0：内触发           |
| x19\~0x1C |         |              |                               |
+-----------+---------+--------------+-------------------------------+
| 0         | 4字节   | FP           |                               |
| x1D\~0x20 |         | GA触发占空比 |                               |
+-----------+---------+--------------+-------------------------------+

2：阀数据包(需要拆包） 通信频率:100ms一次

**UDP数据的最大长度通常小于1472字节（1500字节减去20字节的IP头和8字节的UDP头）**

第1包（阀1-80）：

  ---------------------------------------------------------------------------------------------
  字节序数      字节大小     含义                                 值说明
  ------------- ------------ ------------------------------------ -----------------------------
  0x01\~0x04    4字节        包的种类                             1 表示 收到的第一包阀数据数据

  0x05\~0x08    4字节        包的大小                             整个包的大小长度 1312

  0x09\~0x0C    4字节        喷吹延时X                            喷吹执行延时，X\*800 us

  0x0D\~0x10    4字节        行触发时间(us)                       喷吹执行时间us 800

  0x11\~0x14    4字节        数据总包数                           2(也可能是5包）

  0x15\~0x20    12字节       空                                   待定

  0x21\~0x520   16\*80字节   前80阀，125行执行，补128（16字节）   喷吹指令信息
  ---------------------------------------------------------------------------------------------

第2包（阀81-160）：

  -------------------------------------------------------------------------
  字节序数      字节大小    含义              值说明
  ------------- ----------- ----------------- -----------------------------
  0x01\~0x04    4字节       包的种类          2 表示 收到的第二包数据

  0x05\~0x08    4字节       包的大小          整个包的大小长度 32+16\*F

  0x09\~0x0C    4字节       喷吹延时X         喷吹执行延时，X\*800 us

  0x0D\~0x10    4字节       行触发时间(us)    喷吹执行时间us 800

  0x11\~0x14    4字节       数据总包数        2(也可能是5包）

  0x15\~0x20    12字节      空                待定

  0x21\~0x520   16\*F       F(1-80)阀数据包   喷吹指令信息
  -------------------------------------------------------------------------

第3包（阀161-240）：

  -------------------------------------------------------------------------
  字节序数      字节大小    含义              值说明
  ------------- ----------- ----------------- -----------------------------
  0x01\~0x04    4字节       包的种类          3 表示 收到的第二包数据

  0x05\~0x08    4字节       包的大小          整个包的大小长度 32+16\*F

  0x09\~0x0C    4字节       喷吹延时X         喷吹执行延时，X\*800 us

  0x0D\~0x10    4字节       行触发时间(us)    喷吹执行时间us 800

  0x11\~0x14    4字节       数据总包数        2(也可能是5包）

  0x15\~0x20    12字节      空                待定

  0x21\~0x520   16\*F       F(1-80)阀数据包   喷吹指令信息
  -------------------------------------------------------------------------

第4包（阀241-320）：

  -------------------------------------------------------------------------
  字节序数      字节大小    含义              值说明
  ------------- ----------- ----------------- -----------------------------
  0x01\~0x04    4字节       包的种类          4表示 收到的第二包数据

  0x05\~0x08    4字节       包的大小          整个包的大小长度 32+16\*F

  0x09\~0x0C    4字节       喷吹延时X         喷吹执行延时，X\*800 us

  0x0D\~0x10    4字节       行触发时间(us)    喷吹执行时间us 800

  0x11\~0x14    4字节       数据总包数        2(也可能是5包）

  0x15\~0x20    12字节      空                待定

  0x21\~0x520   16\*F       F(1-80)阀数据包   喷吹指令信息
  -------------------------------------------------------------------------

第5包（阀321-400），第6包（阀401-480），第7包（阀481-512）如上

注：根据设备阀的数量不同，后续还可能有新的拼接包产生，该协议为测试数据。

喷吹指令含义信息详细解析:

测试案例中，设备包含90个阀。

OOOOOOOOOOOOOOOOOOOOOOOOOOO\-\-\-\-\-\-\-\-\-\-\-\-\-\--90个阀

[0 0]{.mark}

[1 0]{.mark}

[0 0]{.mark}

[1 1]{.mark}

[1 1]{.mark}

[1]{.mark}

[0]{.mark}

[1]{.mark}

[1]{.mark}

[1]{.mark}

[1]{.mark}

黄色 字体表示一个8个位 表示 1个字节，当一个位值为1时，代表 喷吹执行时间
800us

图中三个连续的1 表示，持续喷吹800\*3 =2400 us。

其中阀竖向序列数字共125个(125\*800us =
100ms)，超出125。则喷吹指令换到下一个阀上。

喷吹包数据按先喷嘴序号顺序再指令时间顺序的方式排列（即90个125）

如红色字体所示，排列。

注：该解析逻辑目前为测试使用，具体项目中，可能会略有差异。
