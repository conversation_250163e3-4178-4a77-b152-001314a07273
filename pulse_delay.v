module pulse_delay (
    input  wire        clk,
    input  wire        rst_n,
    input  wire        pulse_in,
    input  wire        device_start_stop,
    input  wire [31:0] delay_time,
    output wire        pulse_out,
    output reg  [31:0] pulse_count
);

// 参数定义
parameter CLK_FREQ = 125_000_000;    // 时钟频率125MHz
parameter MS_CYCLES = CLK_FREQ/1000; // 1ms所需时钟周期数
parameter US_CYCLES = CLK_FREQ/1000000; // 1us所需时钟周期数
parameter MAX_DELAY = 600;           // 最大延时600ms
parameter MIN_DELAY = 300;           // 最小延时300ms
parameter MAX_PENDING = 8;           // 最多同时处理8个延时
parameter DETECT_DELAY = 20;         // 检测延时20us
// 内部信号定义
reg [1:0]  pulse_in_r;
wire       pulse_posedge;
reg [31:0] actual_delay;
reg [19:0] delay_counters[MAX_PENDING-1:0]; // 延时计数器数组
reg [MAX_PENDING-1:0] counter_valid;        // 计数器有效标志
reg        output_pulse;
reg [3:0] i;
reg found_empty; 

// 检测逻辑信号
reg [4:0] detect_counter;            // 20us检测计数器
reg       detect_valid;              // 检测使能
reg       detect_pulse_in;           // 检测时pulse_in的电平状态

reg device_start_stop_r;
wire device_start_stop_pos;

// 上升沿检测
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        pulse_in_r <= 2'b00;
        device_start_stop_r <= 1'b0;
    end
    else begin
        pulse_in_r <= {pulse_in_r[0], pulse_in};
        device_start_stop_r <= device_start_stop;
    end
end

assign pulse_posedge = pulse_in_r[0] & ~pulse_in_r[1];
assign device_start_stop_pos = device_start_stop & ~device_start_stop_r;

//检测到上升沿后延时20us并检查pulse_in状态
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        detect_counter <= 0;
        detect_valid <= 0;
        detect_pulse_in <= 0;
    end else begin
        if (pulse_posedge) begin
            // 检测到上升沿，开始20us计时
            detect_valid <= 1;
            detect_counter <= DETECT_DELAY * US_CYCLES;
        end else if (detect_valid) begin
            if (detect_counter > 0) begin
                detect_counter <= detect_counter - 1;
            end else begin
                // 20us时间到，记录pulse_in状态
                detect_valid <= 0;
                detect_pulse_in <= pulse_in;
            end
        end
    end
end

// 延时范围限制
// always @(*) begin
//     if (delay_time < MIN_DELAY)
//         actual_delay = MIN_DELAY;
//     else if (delay_time > MAX_DELAY)
//         actual_delay = MAX_DELAY;
//     else
//         actual_delay = delay_time;
// end

always @(posedge clk or negedge rst_n) begin
    if(!rst_n)begin
        actual_delay <= MIN_DELAY;
    end
    else if (delay_time < MIN_DELAY)
        actual_delay <= MIN_DELAY;
    else if (delay_time > MAX_DELAY)
        actual_delay <= MAX_DELAY;
    else
        actual_delay <= delay_time;
end

// 延时处理逻辑
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        output_pulse <= 1'b0;
        counter_valid <= 0;
        found_empty <= 1'b0;
        pulse_count <= 32'd0;
        for (i = 0; i < MAX_PENDING; i = i + 1) begin
            delay_counters[i] <= 0;
        end
    end else begin
        output_pulse <= 1'b0;
        if(device_start_stop_pos)begin//设备启动后从0开始计数
            pulse_count <= 32'd0;
        end
        // 处理新的输入脉冲
        found_empty <= 1'b0; // 重置找到空闲计数器标志
        if (detect_valid == 0 && detect_pulse_in == 1) begin
            for (i = 0; i < MAX_PENDING; i = i + 1) begin
                if (!counter_valid[i] && !found_empty) begin
                    delay_counters[i] <= actual_delay * MS_CYCLES - DETECT_DELAY * US_CYCLES;
                    counter_valid[i] <= 1'b1;
                    found_empty <= 1'b1; // 找到一个空闲的计数器
                end
            end
        end
        
        // 更新所有计数器
        for (i = 0; i < MAX_PENDING; i = i + 1) begin
            if (counter_valid[i]) begin
                if (delay_counters[i] == 1) begin
                    counter_valid[i] <= 1'b0;
                    output_pulse <= 1'b1;
                    pulse_count <= pulse_count + 1'b1;
                end
                if (delay_counters[i] > 0) begin
                    delay_counters[i] <= delay_counters[i] - 1'b1;
                end
            end
        end
    end
end

assign pulse_out = output_pulse;

// // 脉冲计数
// always @(posedge clk or negedge rst_n) begin
//     if (!rst_n)
//         pulse_count <= 32'd0;
//     else if (pulse_posedge)
//         pulse_count <= pulse_count + 1'b1;
// end

endmodule
