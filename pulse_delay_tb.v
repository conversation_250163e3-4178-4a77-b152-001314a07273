`timescale 1ns/1ps

module pulse_delay_tb;

reg         clk;
reg         rst_n;
reg         pulse_in;
reg  [31:0] delay_time;
wire        pulse_out;
wire [31:0] pulse_count;

// 实例化被测模块
pulse_delay u_pulse_delay (
    .clk        (clk),
    .rst_n      (rst_n),
    .pulse_in   (pulse_in),
    .delay_time (delay_time),
    .pulse_out  (pulse_out),
    .pulse_count(pulse_count)
);

// 时钟生成（125MHz）
initial begin
    clk = 0;
    forever #4 clk = ~clk;  // 8ns周期
end

// 测试激励
initial begin
    // 初始化
    rst_n = 0;
    pulse_in = 0;
    delay_time = 400;  // 设置400ms延时
    
    // 复位释放
    #100 rst_n = 1;
    
    // 产生输入脉冲
    repeat (20) begin
        #100000;     // 等待100ms
        pulse_in = 1;
        #8;
        pulse_in = 0;
    end
    
    // 等待所有延时完成
    #1_000_000;
    $finish;
end

// 添加波形监控
initial begin
    $dumpfile("pulse_delay.vcd");
    $dumpvars(0, pulse_delay_tb);
end

// 添加结果监控
initial begin
    $monitor("Time=%0t ps: pulse_count=%0d, pulse_out=%b", 
             $time, pulse_count, pulse_out);
end

endmodule
