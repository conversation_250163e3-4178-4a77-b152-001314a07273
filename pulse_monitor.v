module pulse_monitor #(
    parameter TIME_TOLERANCE_MS = 2      // 时间容差，默认±10ms
)(
    input  wire        clk,              // 系统时钟 125MHz
    input  wire        rst_n,            // 低电平有效复位
    input  wire        pulse_in,         // 输入脉冲
    input  wire        device_start_stop, // 设备启动/停止信号
    output reg  [31:0] total_pulses,     // 总脉冲计数
    output reg  [31:0] lost_pulses,      // 丢失脉冲计数
    output reg  [31:0] fast_pulses,      // 间隔过短的脉冲计数
    output reg  [31:0] theory_pulses,    // 理论应收到的脉冲数
    output reg  [31:0] slow_pulses       // 间隔过长的脉冲计数（同时也是丢失的）
);

    // 参数定义
    localparam CLK_FREQ = 125_000_000;            // 125MHz时钟
    localparam EXPECTED_MS = 100;                  // 预期100ms间隔
    localparam EXPECTED_CYCLES = (CLK_FREQ/1000) * EXPECTED_MS;
    localparam MIN_CYCLES = (CLK_FREQ/1000) * (EXPECTED_MS - TIME_TOLERANCE_MS);
    localparam MAX_CYCLES = (CLK_FREQ/1000) * (EXPECTED_MS + TIME_TOLERANCE_MS);

    // 内部信号
    reg [1:0]  pulse_r;              // 用于边沿检测
    wire       pulse_posedge;        // 上升沿标志
    reg [31:0] interval_counter;     // 间隔计数器
    reg        first_pulse;          // 第一个脉冲标志
    reg [31:0] lost_update_counter;
    reg [31:0] global_counter;

    wire       device_start_stop_posedge;
    reg        device_start_stop_r;
    reg        start_flag;
    // 上升沿检测
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            pulse_r <= 2'b00;
            device_start_stop_r <= 1'b0;
        end else begin
            pulse_r <= {pulse_r[0], pulse_in};
            device_start_stop_r <= device_start_stop;
        end
    end

    assign pulse_posedge = pulse_r[0] & ~pulse_r[1];
    assign device_start_stop_posedge = device_start_stop_r & ~device_start_stop;

    // 脉冲检测和计数逻辑
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            interval_counter <= 32'd0;
            total_pulses    <= 32'd0;
            lost_pulses     <= 32'd0;
            fast_pulses     <= 32'd0;
            slow_pulses     <= 32'd0;
            theory_pulses   <= 32'd0;
            first_pulse     <= 1'b0;
            lost_update_counter <= 32'd0;
            global_counter <= 32'd0;
            start_flag <= 1'b0;
        end
        else if(device_start_stop_posedge) begin
            start_flag <= 1'b1;
            interval_counter <= 32'd0;
            total_pulses    <= 32'd0;
            lost_pulses     <= 32'd0;
            fast_pulses     <= 32'd0;
            slow_pulses     <= 32'd0;
            theory_pulses   <= 32'd0;
            first_pulse     <= 1'b0;
            lost_update_counter <= 32'd0;
            global_counter <= 32'd0;
        end else if(start_flag) begin
            if (!first_pulse) begin
                // 等待第一个脉冲
                if (pulse_posedge) begin
                    first_pulse      <= 1'b1;
                    interval_counter <= 32'd0;
                    total_pulses    <= 32'd1;
                    theory_pulses   <= 32'd1;
                    lost_update_counter <= 32'd0;
                    global_counter <= 32'd0;
                end
            end
            else begin
                // 计数间隔时间
                interval_counter <= interval_counter + 1'b1;
                global_counter <= global_counter + 1'b1;

                if(global_counter >= EXPECTED_CYCLES)begin
                    global_counter <= 32'd0;
                    theory_pulses <= theory_pulses + 1'b1;
                end

                if(lost_update_counter >= EXPECTED_CYCLES * 10)begin//每秒更新丢失脉冲数
                    lost_update_counter <= 32'd0;
                    lost_pulses <= theory_pulses - total_pulses;
                end
                // 实时更新理论脉冲数
                // if (interval_counter >= last_theory_update + EXPECTED_CYCLES) begin
                //     theory_pulses <= theory_pulses + 1'b1;
                //     last_theory_update <= last_theory_update + EXPECTED_CYCLES;
                // end
                
                if (pulse_posedge) begin
                    total_pulses    <= total_pulses + 1'b1;
                    
                    // 检查脉冲间隔
                    if (interval_counter < MIN_CYCLES) begin
                        fast_pulses <= fast_pulses + 1'b1;
                    end
                    else if (interval_counter > MAX_CYCLES) begin
                        slow_pulses <= slow_pulses + 1'b1;
                        // lost_pulses <= lost_pulses + ((interval_counter - 1) / EXPECTED_CYCLES);
                        // lost_pulses <= theory_pulses - total_pulses;
                    end
                    
                    // 重置计数器
                    interval_counter <= 32'd0;
                end
            end
        end
        // else begin
        //     interval_counter <= 32'd0;
        //     total_pulses    <= 32'd0;
        //     lost_pulses     <= 32'd0;
        //     fast_pulses     <= 32'd0;
        //     slow_pulses     <= 32'd0;
        //     theory_pulses   <= 32'd0;
        //     first_pulse     <= 1'b0;
        //     lost_update_counter <= 32'd0;
        //     global_counter <= 32'd0;
        // end
    end

endmodule
