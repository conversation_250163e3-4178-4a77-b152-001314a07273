// 125MHz时钟秒计数器模块
module second_counter (
    input wire clk,        // 125MHz系统时钟
    input wire rst_n,      // 异步复位(低有效)
    output reg one_sec_sig // 1秒计数信号
    // input wire  [31:0] valve_open_count, // 喷吹阀门开启计数
    // output reg [31:0] valve_open_per_second  //  每秒的阀门开启次数
);

// 参数定义
parameter CLK_FREQ = 125_000_000;  // 时钟频率
parameter CNT_MAX = CLK_FREQ - 1;   // 计数最大值(1秒)

// 信号声明
reg [26:0] cycle_count;  // 周期计数器
// reg [31:0] last_valve_open_count;

// 主计数逻辑
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        cycle_count <= 27'd0;
        // last_valve_open_count <= 32'd0;
        // valve_open_per_second <= 32'd0;
        one_sec_sig <= 1'b0;
    end else begin
        one_sec_sig <= 1'b0;
        if (cycle_count == CNT_MAX) begin
            // last_valve_open_count <= valve_open_count;
            // valve_open_per_second <= valve_open_count - last_valve_open_count;
            cycle_count <= 27'd0;
            one_sec_sig <= 1'b1;
        end else begin
            cycle_count <= cycle_count + 1;
        end
    end
end

endmodule