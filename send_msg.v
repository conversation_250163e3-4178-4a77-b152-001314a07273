module send_msg (
    input           sys_clk,
    input           sys_rst_n,
    input           tx_req,
    input           icmp_tx_busy,
    input           one_sec_sig,
    input           one_pac_done,
    input           device_start_stop,
    input   [31:0]  blow_delay_x,
    input   [31:0]  line_trigger_time,
    input   [31:0]  total_trigger_signals,
    input   [31:0]  lost_triggers_a,
    input   [31:0]  total_data_packets,
    input   [31:0]  valve_freq,
    input   [31:0]  valve_total,
    input   [31:0]  lost_triggers_b,
    input   [31:0]  lost_packets_b,
    input   [31:0]  reserved_field,
    
    output reg         tx_start_en,
    output reg [15:0]  tx_byte_num,
    output reg [7:0]   tx_data
);

// reg [7:0] tx_data;
// reg [15:0] tx_byte_num;
// reg tx_start_en;
// reg [26:0] counter; // 新增27位计数器用于1秒定时
// reg [7:0] data [0:10]; // 存储"hello world"字符串的ASCII码
// reg [3:0] data_index; // 当前发送字符索引

reg [31:0] field_data[0:11];
reg [3:0] byte_cnt;
reg [3:0] field_cnt;
reg [31:0] recv_pac_cnt;
reg [31:0] packet_diff;
// localparam  COUNTER_MAX = 27'd125_000_000;

// always @(posedge sys_clk or negedge sys_rst_n) begin
//     if (!sys_rst_n) begin
//         counter <= 27'd0;
//     end else if (counter == COUNTER_MAX - 1) begin
//         counter <= 27'd0; // 计数器归零
//     end else begin
//         counter <= counter + 27'd1; // 计数器递增
//     end
// end

reg one_pac_done_r;
wire one_pac_done_pos;
assign one_pac_done_pos = one_pac_done & ~one_pac_done_r;

reg device_start_stop_r;
wire device_start_stop_pos;
assign device_start_stop_pos = device_start_stop & ~device_start_stop_r;

always @(posedge sys_clk or negedge sys_rst_n) begin
    if(!sys_rst_n)begin
        one_pac_done_r <= 1'b0;
        device_start_stop_r <= 1'b0;
    end
    else begin
        one_pac_done_r <= one_pac_done;
        device_start_stop_r <= device_start_stop;
    end
end

always@(posedge sys_clk or negedge sys_rst_n) begin
    if(!sys_rst_n)begin
        recv_pac_cnt <= 32'd0;
    end
    else if(device_start_stop_pos)begin
        recv_pac_cnt <= 32'd0;
    end
    else if(one_pac_done_pos)begin
        recv_pac_cnt <= recv_pac_cnt + 32'd1;
    end

end


always @(posedge sys_clk or negedge sys_rst_n) begin
    if(!sys_rst_n) begin
        packet_diff <= 32'd0;
    end else begin
        packet_diff <= total_data_packets - recv_pac_cnt;
    end
end


always @(posedge sys_clk or negedge sys_rst_n) begin
    if(!sys_rst_n)begin
        tx_start_en <= 1'b0;
    // end else if((counter == COUNTER_MAX - 1) && (!icmp_tx_busy)) begin
    end else if(one_sec_sig && (!icmp_tx_busy)) begin
        tx_start_en <= 1'b1;
    end
    else
        tx_start_en <= 1'b0;
end


always @(posedge sys_clk or negedge sys_rst_n) begin
    if (!sys_rst_n) begin
        tx_data <= 8'd0;
        tx_byte_num <= 16'd0;
        byte_cnt <= 4'd0;
        field_cnt <= 4'd0;
    // end else if (counter == COUNTER_MAX - 1) begin
    end else if (one_sec_sig) begin
        tx_byte_num <= 16'd48; // 设置发送字节数为48
        byte_cnt <= 4'd0;
        field_cnt <= 4'd0;
    end else if ( tx_req) begin
        case (byte_cnt)
            4'd0: tx_data <= field_data[field_cnt][31:24];
            4'd1: tx_data <= field_data[field_cnt][23:16];
            4'd2: tx_data <= field_data[field_cnt][15:8];
            4'd3: tx_data <= field_data[field_cnt][7:0];
            default: tx_data <= 8'd0; 
        endcase

        if (byte_cnt == 4'd3) begin
            byte_cnt <= 4'd0; // 重置计数器
            if(field_cnt == 4'd11)begin
                field_cnt <= 4'd0;
                byte_cnt <= 4'd0;
                tx_byte_num <= 16'd0;
            end
            else
                field_cnt <= field_cnt + 4'd1; // 索引递增
        end else begin
            byte_cnt <= byte_cnt + 4'd1; // 计数器递增
        end
    end else begin
        tx_data <= 8'd0;
    end

end


always @(posedge sys_clk or negedge sys_rst_n) begin
    if(!sys_rst_n)begin
        field_data[0] <= 32'h00_00_00_58;
        field_data[1] <= 32'h00_00_00_00;
        field_data[2] <= 32'h00_00_00_00;
        field_data[3] <= 32'h00_00_00_00;
        field_data[4] <= 32'h00_00_00_00;
        field_data[5] <= 32'h00_00_00_00;
        field_data[6] <= 32'h00_00_00_00;
        field_data[7] <= 32'h00_00_00_00;
        field_data[8] <= 32'h00_00_00_00;
        field_data[9] <= 32'h00_00_00_00;
        field_data[10] <= 32'h00_00_00_00;
        field_data[11] <= 32'h00_00_00_00;
    end
    else begin
        field_data[0]  <= 32'h00_00_00_58;
        field_data[1]  <= blow_delay_x;          // 喷吹延时X
        field_data[2]  <= line_trigger_time;     // 行触发时间
        field_data[3]  <= total_trigger_signals; // 触发总数
        field_data[4]  <= lost_triggers_a;       // 丢失触发A
        field_data[5]  <= total_data_packets;    // 数据包总数
        field_data[6]  <= packet_diff;           // 丢失包A
        field_data[7]  <= valve_freq;            // 阀频率
        field_data[8]  <= valve_total;           // 阀总次数
        field_data[9]  <= lost_triggers_b;       // 丢失触发B
        field_data[10] <= lost_packets_b;        // 丢失包B
        field_data[11] <= reserved_field;        // 待定字段
    end
end

// always @(posedge sys_clk or negedge sys_rst_n) begin
//     if (!sys_rst_n) begin
//         tx_data <= 8'd0;
//         tx_byte_num <= 16'd0;
//         tx_start_en <= 1'b0;
//         data_index <= 4'd0;
//         // 初始化"hello world"字符串的ASCII码
//         data[0] <= "h"; data[1] <= "e"; data[2] <= "l"; data[3] <= "l";
//         data[4] <= "o"; data[5] <= " "; data[6] <= "w"; data[7] <= "o";
//         data[8] <= "r"; data[9] <= "l"; data[10] <= "d"; // 修正最后一个字符为'd'
//     end else if (counter == COUNTER_MAX - 1) begin
//         tx_start_en <= 1'b1; // 触发发送
//         tx_byte_num <= 16'd11; // 设置发送字节数为11
//         data_index <= 4'd0; // 重置数据索引
//     end else if (tx_start_en & tx_req) begin
//         tx_data <= data[data_index]; // 发送当前字符
//         data_index <= data_index + 4'd1; // 索引递增
//         if (data_index == 4'd10) begin
//             tx_start_en <= 1'b0; // 发送完成，关闭使能
//             data_index <= 4'd0; // 重置索引
//         end
//     end
// end







    
endmodule