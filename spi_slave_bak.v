// SPI Slave Module with Configurable Parameters
// 支持所有SPI模式 (CPOL/CPHA) 和可调数据位宽
module spi_slave #(
    parameter DATA_WIDTH = 8,        // 数据位宽 (默认8位)
    parameter SPI_MODE = 0           // SPI模式 (0-3)
)(
    // 系统接口
    input wire clk,                  // 系统时钟 (至少4倍于sclk)
    input wire rst_n,                // 异步复位 (低有效)
    
    // SPI接口
    input wire sclk,                 // SPI时钟
    input wire cs_n,                 // 片选 (低有效)
    input wire mosi,                 // 主出从入
    output reg miso,                 // 主入从出
    
    // 用户接口
    output reg [7:0] rx_data_out/* synthesis syn_preserve=1 */, // 接收数据
    // output reg rx_valid,             // 接收有效脉冲
    input wire [DATA_WIDTH-1:0] tx_data, // 发送数据
    output reg fifo_wr_en,        // FIFO写使能
    output reg [7:0] rec_byte,     // 接收的字节
    output reg transmit_done,     // 传输完成
    output reg tx_ready,             // 发送就绪 (可以加载新数据)
    output reg error                 // 错误指示 (CSn提前变化)
);

/* SPI模式对照表：
   Mode | CPOL | CPHA
   -------------------
     0 |   0  |   0
     1 |   0  |   1
     2 |   1  |   0
     3 |   1  |   1
*/
// 模式配置参数 ---------------------------------------------------
localparam CPOL = (SPI_MODE == 2 || SPI_MODE == 3) ? 1'b1 : 1'b0;
localparam CPHA = (SPI_MODE == 1 || SPI_MODE == 3) ? 1'b1 : 1'b0;

// 同步寄存器 -----------------------------------------------------
reg [2:0] sclk_sync;
reg [2:0] cs_n_sync;
reg [2:0] mosi_sync/* synthesis syn_preserve=1 */;
// reg mosi_sync;
// reg mosi_sync_r1/* synthesis syn_keep=1 */;
// reg mosi_sync_r2/* synthesis syn_keep=1 */;

// 边沿检测 -------------------------------------------------------
wire sclk_rise  = (sclk_sync[2:1] == 2'b01);
wire sclk_fall  = (sclk_sync[2:1] == 2'b10);
wire cs_active  = !cs_n_sync[1];            // 有效片选
wire cs_start   = (cs_n_sync[2:1] == 2'b10);// 片选下降沿
wire cs_end     = (cs_n_sync[2:1] == 2'b01);// 片选上升沿

// 控制信号 -------------------------------------------------------
reg [DATA_WIDTH-1:0] rx_shift/* synthesis syn_preserve=1 */;       // 接收移位寄存器
reg [DATA_WIDTH-1:0] tx_shift;       // 发送移位寄存器
reg [3:0] bit_cnt;                   // 位计数器
reg phase;                           // 相位跟踪
// reg cs_active_prev;                  // 前周期片选状态

// 同步过程 -------------------------------------------------------
always @(posedge clk or negedge rst_n) begin
    if(!rst_n) begin
        sclk_sync  <= 3'b0;
        cs_n_sync  <= 3'b111;
        mosi_sync  <= 3'b0;
        // mosi_sync  <= 1'b0;
        // mosi_sync_r1 <= 1'b0;
        // mosi_sync_r2 <= 1'b0;
    end else begin
        sclk_sync  <= {sclk_sync[1:0], sclk};
        cs_n_sync  <= {cs_n_sync[1:0], cs_n};
        mosi_sync  <= {mosi_sync[1:0], mosi};
        // mosi_sync[2] <= mosi_sync[1];
        // mosi_sync[1] <= mosi;
        // mosi_sync_r2 <= mosi_sync_r1;
        // mosi_sync_r1 <= mosi;
    end
end

// 主控制逻辑 -----------------------------------------------------
always @(posedge clk or negedge rst_n) begin
    if(!rst_n) begin
        rx_data_out     <= 8'd0;
        // rx_valid    <= 0;
        tx_shift    <= 0;
        rx_shift    <= 8'd0;
        miso        <= 1'bz;
        bit_cnt     <= 4'd8;
        phase       <= 0;
        tx_ready    <= 1;
        error       <= 0;
        fifo_wr_en  <= 0;
        transmit_done <= 0;
        rec_byte <= 8'd0;
        // cs_active_prev <= 0;
    end else begin
        // rx_valid <= 0;
        error    <= 0;
        tx_ready <= 0;
        fifo_wr_en <= 0;
        transmit_done <= 1'b0;
        // 片选状态变化检测
        // cs_active_prev <= cs_active;

        if(cs_start) begin
            // 片选激活时初始化
            bit_cnt  <= 4'd8;
            phase    <= CPHA;
            tx_shift <= tx_data;
            tx_ready <= 0;
            rec_byte <= 8'd0;
            miso     <= (CPHA == 0) ? tx_data[DATA_WIDTH-1] : 1'bz;
            rx_shift <= 8'd0;
        end
        else if(cs_active) begin
            // 根据SPI模式处理时钟边沿
            case({CPOL, CPHA})
                2'b00: begin // Mode 0
                    if(sclk_rise) begin
                        phase <= 1;
                    end
                    else if(sclk_fall) begin
                        phase <= 0;
                        rx_shift <= {rx_shift[DATA_WIDTH-2:0], mosi_sync[1]};// 接收数据
                        // 发送数据
                        if(bit_cnt > 0) begin
                            miso <= tx_shift[DATA_WIDTH-1];
                            tx_shift <= {tx_shift[DATA_WIDTH-2:0], 1'b0};
                            bit_cnt <= bit_cnt - 4'd1;
                        end
                        // 数据包接收完成
                        if(bit_cnt == 4'd1) begin
                            rx_data_out <= {rx_shift[DATA_WIDTH-2:0], mosi_sync[1]};
                            fifo_wr_en <= 1;
                            bit_cnt <= 4'd8;   // 自动开始下一次接收
                            tx_shift <= tx_data;     // 加载新的发送数据
                            rec_byte <= rec_byte + 8'd1;
                        end
                    end
                end
                2'b01: begin // Mode 1
                    if(sclk_fall) phase <= 1;
                    if(sclk_rise) begin
                        phase <= 0;
                        rx_shift <= {rx_shift[DATA_WIDTH-2:0], mosi_sync[1]};// 接收数据
                        // 发送数据
                        if(bit_cnt > 0) begin
                            miso <= tx_shift[DATA_WIDTH-1];
                            tx_shift <= {tx_shift[DATA_WIDTH-2:0], 1'b0};
                            bit_cnt <= bit_cnt - 1;
                        end
                        // 数据包接收完成
                        if(bit_cnt == 1) begin
                            rx_data_out <= {rx_shift[DATA_WIDTH-2:0], mosi_sync[1]};
                            fifo_wr_en <= 1;
                            bit_cnt <= DATA_WIDTH;   // 自动开始下一次接收
                            tx_shift <= tx_data;     // 加载新的发送数据
                            rec_byte <= rec_byte + 8'd1;
                        end
                    end
                end
                2'b10: begin // Mode 2
                    if(sclk_fall) phase <= 1;
                    if(sclk_rise) begin
                        phase <= 0;
                        rx_shift <= {rx_shift[DATA_WIDTH-2:0], mosi_sync[1]};// 接收数据
                        // 发送数据
                        if(bit_cnt > 0) begin
                            miso <= tx_shift[DATA_WIDTH-1];
                            tx_shift <= {tx_shift[DATA_WIDTH-2:0], 1'b0};
                            bit_cnt <= bit_cnt - 1;
                        end
                        // 数据包接收完成
                        if(bit_cnt == 1) begin
                            rx_data_out <= {rx_shift[DATA_WIDTH-2:0], mosi_sync[1]};
                            fifo_wr_en <= 1;
                            bit_cnt <= DATA_WIDTH;   // 自动开始下一次接收
                            tx_shift <= tx_data;     // 加载新的发送数据
                            rec_byte <= rec_byte + 8'd1;
                        end
                    end
                end
                2'b11: begin // Mode 3
                    if(sclk_rise) phase <= 1;
                    if(sclk_fall) begin
                        phase <= 0;
                        rx_shift <= {rx_shift[DATA_WIDTH-2:0], mosi_sync[1]};// 接收数据
                        // 发送数据
                        if(bit_cnt > 0) begin
                            miso <= tx_shift[DATA_WIDTH-1];
                            tx_shift <= {tx_shift[DATA_WIDTH-2:0], 1'b0};
                            bit_cnt <= bit_cnt - 1;
                        end
                        // 数据包接收完成
                        if(bit_cnt == 1) begin
                            rx_data_out <= {rx_shift[DATA_WIDTH-2:0], mosi_sync[1]};
                            fifo_wr_en <= 1;
                            bit_cnt <= DATA_WIDTH;   // 自动开始下一次接收
                            tx_shift <= tx_data;     // 加载新的发送数据
                            rec_byte <= rec_byte + 8'd1;
                        end
                    end
                end
            endcase
            // if(bit_cnt == 1 && 
            //    ((CPOL == 0 && CPHA == 0 && sclk_fall) || 
            //     (CPOL == 0 && CPHA == 1 && sclk_rise) ||
            //     (CPOL == 1 && CPHA == 0 && sclk_rise) ||
            //     (CPOL == 1 && CPHA == 1 && sclk_fall))) begin
            //     // rx_valid <= 1;
            //     fifo_wr_en <= 1;
            //     bit_cnt <= DATA_WIDTH;   // 自动开始下一次接收
            //     tx_shift <= tx_data;     // 加载新的发送数据
            //     rec_byte <= rec_byte + 8'd1;
            //     // miso     <= (CPHA == 0) ? tx_data[DATA_WIDTH-1] : 1'bz;
            // end

            // // 检查意外片选变化
            // if(cs_active_prev && !cs_active) begin
            //     error <= 1;
            // end
        end
        else if(cs_end) begin
            // 传输结束处理
            miso <= 1'bz;
            tx_ready <= 1;
            transmit_done <= 1'b1;
            if(bit_cnt != DATA_WIDTH) begin // 非完整传输
                error <= 1;
            end
        end
    end
end

// 数据移位处理任务 -----------------------------------------------
// task handle_shift;
// begin
//     // 接收数据
//     rx_shift <= {rx_shift[DATA_WIDTH-2:0], mosi_sync[1]};
    
//     // 发送数据
//     if(bit_cnt > 0) begin
//         miso <= tx_shift[DATA_WIDTH-1];
//         tx_shift <= {tx_shift[DATA_WIDTH-2:0], 1'b0};
//         bit_cnt <= bit_cnt - 1;
//     end
    
//     // 数据包接收完成
//     if(bit_cnt == 1) begin
//         rx_data <= {rx_shift[DATA_WIDTH-2:0], mosi_sync[1]};
//     end
// end
// endtask

endmodule