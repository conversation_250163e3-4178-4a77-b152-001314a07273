`timescale 1ns/1ps

module tb_distribute_save();

// 定义参数和寄存器
reg               sys_clk;
reg               sys_rst_n;
reg               in_valid;
reg               one_pac_done;
reg     [127:0]   in_data;
    // FIFO接口信号
reg  [7:0]  fifo_data;
reg         fifo_empty;
wire        fifo_rd_en;
    
reg udp_rx_done;
reg               tx_en;
// 定义线网
wire              in_ready;
wire              out_valid;
wire              next_data_req;
wire    [7:0]     fifo_wr_en;
wire    [7:0]     ram_wr_addr;
wire    [63:0]    ram_wr_data;
wire    [7:0]     ram_rd_addr;
wire    [63:0]    ram_rd_data;

reg grs_n;
GTP_GRS GRS_INST(
        .GRS_N(grs_n)
);

initial begin
    grs_n = 1'b0;
    #500 grs_n = 1'b1;
end
// 实例化被测模块
//distribute_save u_distribute_save(
//    .sys_clk        (sys_clk),
//    .sys_rst_n      (sys_rst_n),
//    .in_valid       (in_valid),
//    .one_pac_done   (one_pac_done),
//    .in_data        (in_data),
//    .in_ready       (in_ready),
//    .out_valid      (out_valid),
//    .next_data_req  (next_data_req),
//    .tx_en          (tx_en),
//    .fifo_wr_en     (fifo_wr_en),
//    .ram_wr_addr    (ram_wr_addr),
//    .ram_wr_data    (ram_wr_data),
//    .ram_rd_addr    (ram_rd_addr),
//    .ram_rd_data    (ram_rd_data)
//);

distribute_save u_distribute_save
    (
    .sys_clk(sys_clk),              // input
    .sys_rst_n(sys_rst_n),          // input
    .in_valid(in_valid),            // input
    .one_pac_done(one_pac_done),    // input
    .in_data(in_data),              // input
    .in_ready(in_ready),            // output
    .out_valid(out_valid),          // output
    .next_data_req(next_data_req),  // output
    .tx_en(tx_en),                  // input
    .fifo_wr_en(fifo_wr_en),        // output
    .ram_wr_addr(ram_wr_addr),      // output
    .ram_wr_data(ram_wr_data),      // output
    .ram_rd_addr(ram_rd_addr),      // output
    .ram_rd_data(ram_rd_data)       // output
);

// 时钟生成
initial begin
    sys_clk = 0;
    forever #4 sys_clk = ~sys_clk;
end

// 测试激励
initial begin
    // 初始化
    sys_rst_n = 0;
    in_valid = 0;
    one_pac_done = 0;
    in_data = 128'h0;
    tx_en = 0;
    // 复位释放
    #100;
    sys_rst_n = 1;
    #20;
    
    // 连续发送64组数据
    repeat(64) begin
        @(posedge sys_clk);
        in_valid = 1;
        // 每次发送不同的测试数据
        // in_data = {$random, $random, $random, $random};
        in_data = {32'h11_22_33_44, 32'h11_22_33_44, 32'h11_22_33_44, 32'h11_22_33_44};
        // 等待next_data_req信号
        @(posedge next_data_req or posedge out_valid);
        #10 in_valid = 0;
        
        // 等待一个时钟周期再发送下一组
        @(posedge sys_clk);
    end

    // 等待数据处理完成
    // wait(out_valid);
    #200;
    $display("out_valid here========================!");
    #100;
    tx_en = 1;
    // #8;
    // tx_en = 0;
    // 等待处理完成
   #1100000;
    
    // 测试结束
    $display("Simulation finished!");
    $stop;
end

// 监控输出
// initial begin
//     $monitor("Time=%0t out_valid=%b ram_wr_data=%h fifo_wr_en=%b",
//              $time, out_valid, ram_wr_data, fifo_wr_en);
// end

endmodule
