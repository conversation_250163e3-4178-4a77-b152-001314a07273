`timescale 1ns/1ps
module tb_parse_msg();

//---------------------------------------
// 时钟与复位信号
//---------------------------------------
reg         clk;
reg         rst_n;
initial begin
    clk = 0;
    forever #4 clk = ~clk; // 125MHz时钟
end

initial begin
    rst_n = 0;
    #20 rst_n = 1; // 20ns后释放复位
end

//---------------------------------------
// 模块接口信号
//---------------------------------------
wire        fifo_rd_en;
reg         rx_done;
reg  [15:0] total_bytes;
reg  [7:0]  fifo_data;

wire        device_enable;
wire [31:0] blow_delay_x;
wire [31:0] line_trigger;
wire [31:0] total_packets;
wire        parser_valid;
wire        error_flag;

//---------------------------------------
// 实例化被测模块
//---------------------------------------
parse_msg u_parse_msg(
    .clk            (clk),
    .rst_n          (rst_n),
    .rx_done        (rx_done),
    .total_bytes    (total_bytes),
    .fifo_data      (fifo_data),
    .fifo_rd_en     (fifo_rd_en),
    .device_enable  (device_enable),
    .blow_delay_x   (blow_delay_x),
    .line_trigger   (line_trigger),
    .total_packets  (total_packets),
    .parser_valid   (parser_valid),
    .error_flag     (error_flag)
);

//---------------------------------------
// 测试控制变量
//---------------------------------------
integer test_case = 0;
reg [7:0] packet_mem [0:31]; // 存储测试数据包

//---------------------------------------
// 主要测试流程
//---------------------------------------
initial begin
    $dumpfile("wave.vcd");
    $dumpvars(0, tb_parse_msg);
    
    // 初始化输入
    rx_done = 0;
    total_bytes = 0;
    fifo_data = 0;
    
    // 等待复位完成
    #30;
    
    // 测试案例1：正常数据包解析
    test_case = 1;
    gen_normal_packet();
    send_packet();
check_result(
    1'b1,            // exp_enable
    32'h12345678,    // exp_delay (喷吹延时)
    32'hAABBCCDD,    // exp_trigger (触发时间)
    32'h00000005     // exp_packets (总包数)
);
    
    // 测试案例2：包长度不足
    // test_case = 2;
    // gen_short_packet();
    // send_packet();
    // check_error();
    
    // 测试案例3：错误包头类型
    // test_case = 3;
    // gen_wrong_header();
    // send_packet();
    // check_error();
    
    // 测试案例4：非法包数范围
    // test_case = 4;
    // gen_invalid_packet_num();
    // send_packet();
    // check_error();
    
    #100;
    $display("All testcases passed!");
    $finish;
end

//---------------------------------------
// 任务定义
//---------------------------------------

// 生成正常数据包
task gen_normal_packet;
    integer i;
    begin
        // 包头（4字节）
        packet_mem[0] = 8'h00;
        packet_mem[1] = 8'h00;
        packet_mem[2] = 8'h00;
        packet_mem[3] = 8'h00;
        
        // 控制字段（最高位使能）
        packet_mem[4] = 8'h00; // device_enable=1
        packet_mem[5] = 8'h00;
        packet_mem[6] = 8'h00;
        packet_mem[7] = 8'h01;
        
        // 喷吹延时X
        packet_mem[8]  = 8'h12;
        packet_mem[9]  = 8'h34;
        packet_mem[10] = 8'h56;
        packet_mem[11] = 8'h78;
        
        // 行触发时间
        packet_mem[12] = 8'hAA;
        packet_mem[13] = 8'hBB;
        packet_mem[14] = 8'hCC;
        packet_mem[15] = 8'hDD;
        
        // 总包数（5）
        packet_mem[16] = 8'h00;
        packet_mem[17] = 8'h00;
        packet_mem[18] = 8'h00;
        packet_mem[19] = 8'h05;

        //触发时间
        packet_mem[20] = 8'h06;
        packet_mem[21] = 8'h07;
        packet_mem[22] = 8'h08;
        packet_mem[23] = 8'h0a;

        //触发方式
        packet_mem[24] = 8'h10;
        packet_mem[25] = 8'h20;
        packet_mem[26] = 8'h30;
        packet_mem[27] = 8'h40;

        //占空比
        packet_mem[28] = 8'h01;
        packet_mem[29] = 8'h02;
        packet_mem[30] = 8'h03;
        packet_mem[31] = 8'h04;

        
        total_bytes = 32; // 8个字段*4字节
    end
endtask

// 发送数据包任务
task send_packet;
    integer i;
    begin
        rx_done = 1;
        #10 rx_done = 0;
        
        for(i=0; i<total_bytes; i=i+1) begin
            // 带命名的循环块
            begin : byte_transfer
                forever begin
                    @(posedge clk);
                    if(fifo_rd_en) begin
                        fifo_data <= packet_mem[i];
                        #1; // 保持数据稳定
                        disable byte_transfer; // 退出当前字节传输循环
                    end
                end
            end
        end
    end
endtask

// 结果检查任务
task check_result;
    input exp_enable;
    input [31:0] exp_delay;
    input [31:0] exp_trigger;
    input [31:0] exp_packets;
    begin
        wait(parser_valid);
        if(device_enable !== exp_enable ||
           blow_delay_x !== exp_delay ||
           line_trigger !== exp_trigger ||
           total_packets !== exp_packets) begin
            $display("Testcase %0d FAILED!", test_case);
            $finish;
        end
        #20;
        $display("Testcase %0d PASSED", test_case);
    end
endtask

// 错误检查任务
task check_error;
    begin
        wait(error_flag);
        if(parser_valid !== 1'b0) begin
            $display("Testcase %0d FAILED: error_flag raised but parser_valid active", test_case);
            $finish;
        end
        #20;
        $display("Testcase %0d PASSED", test_case);
    end
endtask

endmodule