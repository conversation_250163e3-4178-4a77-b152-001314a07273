`timescale 1ns/1ps
module spi_slave_tb;

// 参数定义
parameter DATA_WIDTH = 8;
parameter SPI_MODE = 0;
localparam CPOL = (SPI_MODE == 2 || SPI_MODE == 3) ? 1 : 0;
localparam CLK_PERIOD = 8;  // 125MHz系统时钟

// 接口信号声明
reg clk, rst_n;
reg sclk, cs_n, mosi;
wire miso;
reg [DATA_WIDTH-1:0] tx_data;
wire [DATA_WIDTH-1:0] rx_data;
wire rx_valid, tx_ready, error;

reg [DATA_WIDTH-1:0] miso_shifter;
reg [DATA_WIDTH-1:0] mosi_shifter;

// 实例化被测模块
spi_slave #(
    .DATA_WIDTH(DATA_WIDTH),
    .SPI_MODE(SPI_MODE)
) uut (
    .clk(clk),
    .rst_n(rst_n),
    .sclk(sclk),
    .cs_n(cs_n),
    .mosi(mosi),
    .miso(miso),
    .rx_data(rx_data),
    .rx_valid(rx_valid),
    .tx_data(tx_data),
    .tx_ready(tx_ready),
    .error(error)
);

// 时钟生成
initial begin
    clk = 0;
    forever #(CLK_PERIOD/2) clk = ~clk;
end

// SPI时钟生成任务
task spi_clock_gen;
    input integer cycles;
    begin
        repeat(cycles) begin
            sclk = CPOL;
            #(CLK_PERIOD*5);
            sclk = ~CPOL;
            miso_shifter = {miso_shifter[DATA_WIDTH-2:0], miso};
            #(CLK_PERIOD*5);
        end
    end
endtask

// 主测试流程
initial begin
    // 初始化
    rst_n = 1;
    cs_n = 1;
    mosi = 0;
    sclk = 1;
    miso_shifter = 8'h0;
    mosi_shifter = 8'h0;
    tx_data = 8'h00;
    #(CLK_PERIOD*5);
    rst_n = 0;
    #(CLK_PERIOD*5);
    rst_n = 1;


    // 测试用例1：正常传输
    $display("\nTest Case 1: Normal Transfer");
    tx_data = 8'hA5;
    spi_transfer(8'h55);  // 发送0x55，接收0xA5

    // 测试用例2：错误检测
    // $display("\nTest Case 2: Error Detection");
    // spi_transfer_error();

    #100;
    $finish;
end

// SPI传输任务
task spi_transfer;
    input [DATA_WIDTH-1:0] send_data;
    begin
        cs_n = 0;
        mosi_shifter = send_data;
        #(CLK_PERIOD*5);
        repeat(DATA_WIDTH) begin
            mosi = mosi_shifter[DATA_WIDTH-1];  // 输出当前最高位
            spi_clock_gen(1);                   // 生成完整时钟周期
            mosi_shifter = {mosi_shifter[DATA_WIDTH-2:0], 1'b0}; // 左移位
        end
        $display("result=: Received 0x%h (Expected 0x%h),get 0x%h (send 0x%h)", rx_data, send_data,miso_shifter,tx_data);
        mosi_shifter = 8'h25;
        tx_data = 8'h5A;
        repeat(DATA_WIDTH) begin
            mosi = mosi_shifter[DATA_WIDTH-1];  // 输出当前最高位
            spi_clock_gen(1);                   // 生成完整时钟周期
            mosi_shifter = {mosi_shifter[DATA_WIDTH-2:0], 1'b0}; // 左移位
        end
        $display("result=: Received 0x%h (Expected 8'h25),get 0x%h (send 0x%h)", rx_data,miso_shifter,tx_data);
        #(CLK_PERIOD);
        cs_n = 1;
        #(CLK_PERIOD*5);
        
        // if(rx_data === send_data&&miso_shifter === tx_data)
        //     $display("PASS: Received 0x%h (Expected 0x%h)", rx_data, send_data);
        // else
        //     $display("ERROR: TX 0x%h (Got 0x%h) | RX 0x%h (Expected 0x%h)",
        //             tx_data, miso_shifter, rx_data, send_data);    
    end
endtask

// SPI错误检测任务
task spi_transfer_error;
    begin
        cs_n = 0;
        #(CLK_PERIOD*5);
        spi_clock_gen(2);
        cs_n = 1;  // 提前拉高片选
        #(CLK_PERIOD*5);
        
        if(error)
            $display("PASS: Error detected");
        else
            $display("ERROR: No error detected");
    end
endtask

// 波形记录
initial begin
    $dumpfile("spi_slave_tb.vcd");
    $dumpvars(0, spi_slave_tb);
end

endmodule