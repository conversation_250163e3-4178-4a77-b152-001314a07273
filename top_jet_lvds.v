module top_jet_lvds (
    // 系统信号
    input         sys_clk,        // 主时钟
    input         sys_rst_n,      // 系统复位(低有效)

    input         rd_rst,         //FIFO读使能
    input         lvds_tx_en,     // LVDS TX使能

    // FIFO接口
    input  [63:0] ram_wr_data,    // RAM写数据
    input  [9:0]  ram_wr_addr,    // RAM写地址
    input         fifo_wr_en,     // FIFO写使能
    input  [9:0]  ram_rd_addr,    // RAM读地址

    // LVDS输出
    output  [3:0] lvds_out,       // LVDS差分输出
    output        tx_done         // 发送完成标志
);

wire [63:0] ram_rd_data;

jet_cmd u_jet_cmd (
    .wr_data    (ram_wr_data    ), 
    .wr_addr    (ram_wr_addr    ), 
    .wr_en      (fifo_wr_en     ), 
    .wr_clk     (sys_clk        ), 
    .wr_rst     (~sys_rst_n     ), 
    .rd_addr    (ram_rd_addr    ), 
    .rd_data    (ram_rd_data    ), 
    .rd_clk     (sys_clk        ), 
    .rd_rst     (rd_rst         )
);

lvds_tx u_lvds_tx (
    .clk        (sys_clk        ), 
    .rst_n      (sys_rst_n      ), 
    .tx_en      (lvds_tx_en     ), 
    .tx_data    (ram_rd_data    ), 
    .tx_done    (tx_done        ), 
    .lvds_out   (lvds_out       )
);

endmodule