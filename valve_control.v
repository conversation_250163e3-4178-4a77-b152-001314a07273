module valve_control(
    input               sys_clk,        // 系统时钟
    input               sys_rst_n,      // 系统复位，低有效
    
    // ETH RGMII接口
    input              eth_rxc,        // RGMII接收时钟
    input              eth_rx_ctl,     // RGMII接收数据控制信号
    input      [3:0]   eth_rxd,        // RGMII接收数据
    output             eth_txc,        // RGMII发送时钟
    output             eth_tx_ctl,     // RGMII发送数据控制信号
    output     [3:0]   eth_txd,        // RGMII发送数据
    output             eth_rst_n,      // 以太网芯片复位信号，低电平有效
    
    //spi接口信号
     input              spi_mosi,     // SPI数据输入
     input              spi_sclk,     // SPI时钟
     input              spi_cs_n,     // SPI片选
     output             spi_miso,     // SPI数据输出

    // // FIFO接口信号
    // input              rd_en,
    // output     [7:0]   rd_data,
    output wire    out,
    // 控制信号
    input              pulse_in,       // 脉冲输入信号

    output     [3:0]   lvds_out_board_1, // 板1 LVDS输出
    output     [3:0]   lvds_out_board_2, // 板2 LVDS输出
    output     [3:0]   lvds_out_board_3, // 板3 LVDS输出
    output     [3:0]   lvds_out_board_4, // 板4 LVDS输出
    output     [3:0]   lvds_out_board_5, // 板5 LVDS输出
    output     [3:0]   lvds_out_board_6, // 板6 LVDS输出
    output     [3:0]   lvds_out_board_7, // 板7 LVDS输出
    output     [3:0]   lvds_out_board_8, // 板8 LVDS输出
    output     [3:0]   lvds_out_board_9, // 板9 LVDS输出
    output     [3:0]   lvds_out_board_10 // 板10 LVDS输出
);

// Parameter define - Network Configuration
parameter BOARD_MAC      = 48'h00_11_22_33_44_55;      // 本板MAC地址
parameter BOARD_IP       = {8'd192,8'd168,8'd1,8'd101};// 本板IP地址 
parameter DES_MAC        = 48'hff_ff_ff_ff_ff_ff;      // 目的MAC地址
parameter DES_IP         = {8'd192,8'd168,8'd1,8'd102};// 目的IP地址

// Parameter define - System Configuration
parameter CLK_FREQ      = 125_000_000;  // 系统时钟频率125MHz
parameter MS_CYCLES     = CLK_FREQ/1000; // 1ms对应时钟周期数
parameter MAX_DELAY     = 600;           // 最大延时600ms
parameter MIN_DELAY     = 300;           // 最小延时300ms
parameter MAX_PENDING   = 8;             // 最多同时处理8个延时
parameter TIME_TOLERANCE_MS = 10;        // 延时容差10ms

parameter DATA_WIDTH = 8;        // 数据位宽 (默认8位)
parameter SPI_MODE = 0;          // SPI模式 (0-3)
// 内部信号定义
wire            udp_rec_done;    // UDP接收完成信号
wire    [7:0]   fifo_data;      // FIFO读出数据
wire            fifo_empty;     // FIFO空标志
wire            fifo_rd_en;     // FIFO读使能
wire            tx_req;         // UDP发送请求
wire            icmp_tx_busy;   // ICMP发送忙
wire            tx_start_en;    // UDP发送开始使能
wire    [15:0]  tx_byte_num;    // UDP发送字节数
wire    [7:0]   tx_data;       // UDP发送数据
wire            clock_125M;
wire    [7:0]   spi_tx_data;
assign out = spi_tx_data[0];
// wire            clock_125M_f;
// GTP_CLKBUFG u_clk125M_bufg (
// .CLKOUT  (clock_125M),
// .CLKIN   (clock_125M_f )
// );
//以太网UDP模块例化
eth_udp #(
    .BOARD_MAC      (BOARD_MAC),     
    .BOARD_IP       (BOARD_IP), 
    .DES_MAC        (DES_MAC),
    .DES_IP         (DES_IP)
) u_eth_udp(
    .sys_clk          (sys_clk),
    .sys_rst_n        (sys_rst_n),
    
    .udp_parse_req    (fifo_rd_en),
    .udp_tx_data      (fifo_data),
    .fifo_empty       (fifo_empty),
    .rec_pkt_done     (udp_rec_done),
    
    .tx_start_en_new  (tx_start_en),
    .tx_byte_num_new  (tx_byte_num),
    .tx_data_new      (tx_data),
    .udp_tx_req       (tx_req),
    .icmp_tx_busy     (icmp_tx_busy),
    
    .gmii_rx_clk      (clock_125M),

    .eth_rxc          (eth_rxc),
    .eth_rx_ctl       (eth_rx_ctl),
    .eth_rxd          (eth_rxd),
    .eth_txc          (eth_txc),
    .eth_tx_ctl       (eth_tx_ctl),
    .eth_txd          (eth_txd),
    .eth_rst_n        (eth_rst_n)
);

//数据分发存储模块例化
distribute_save #(
    .CLK_FREQ       (CLK_FREQ),    
    .MS_CYCLES      (MS_CYCLES),     
    .MAX_DELAY      (MAX_DELAY),     
    .MIN_DELAY      (MIN_DELAY),     
    .MAX_PENDING    (MAX_PENDING),
    .TIME_TOLERANCE_MS (TIME_TOLERANCE_MS)    
) u_distribute_save(
    .sys_clk          (clock_125M),
    .sys_rst_n        (sys_rst_n),
    
    .pulse_in         (pulse_in),
    
    .fifo_data        (fifo_data),
    .fifo_empty       (fifo_empty),
    .fifo_rd_en       (fifo_rd_en),
    .udp_rx_done      (udp_rec_done),
    
    .tx_req           (tx_req),
    .icmp_tx_busy     (icmp_tx_busy),
    .tx_start_en      (tx_start_en),
    .tx_byte_num      (tx_byte_num),
    .tx_data          (tx_data),
    
    .lvds_out_board_1 (lvds_out_board_1),
    .lvds_out_board_2 (lvds_out_board_2),
    .lvds_out_board_3 (lvds_out_board_3),
    .lvds_out_board_4 (lvds_out_board_4),
    .lvds_out_board_5 (lvds_out_board_5),
    .lvds_out_board_6 (lvds_out_board_6),
    .lvds_out_board_7 (lvds_out_board_7),
    .lvds_out_board_8 (lvds_out_board_8),
    .lvds_out_board_9 (lvds_out_board_9),
    .lvds_out_board_10 (lvds_out_board_10)
);



top_spi_fifo #(
    .DATA_WIDTH(DATA_WIDTH),
    .SPI_MODE(SPI_MODE)
) u_spi_interface(
    .clk(clock_125M),                  // input
    .rst_n(sys_rst_n),              // input
    .spi_mosi(spi_mosi),        // input
    .spi_sclk(spi_sclk),        // input
    .spi_cs_n(spi_cs_n),        // input
    .spi_miso(spi_miso),        // output
    .spi_tx_data(spi_tx_data)   // output
);
// top_spi_fifo
//     #(
//     .DATA_WIDTH(DATA_WIDTH),
//     .SPI_MODE(SPI_MODE)
//     ) u_spi_interface
//     (
//     .clk(clock_125M),                      // input
//     .rst_n(sys_rst_n),                  // input

//     .spi_mosi(spi_mosi),            // input
//     .spi_sclk(spi_sclk),            // input
//     .spi_cs_n(spi_cs_n),            // input
//     .spi_miso(spi_miso),            // output
//     .spi_tx_data()

//     // .rd_en(rd_en),                  // input
//     // .rd_data(rd_data),              // output
//     // .tx_data(tx_data),              // input
//     // .transmit_done(transmit_done),  // output
//     // .rx_valid(rx_valid)            // output
//     // .rec_byte(rec_byte)             // output
// );

// data_pll u_data_pll (
//   .pll_rst(1'b0),      // input
//   .clkin1(eth_rxc),        // input
//   .pll_lock(pll_lock),    // output
//   .clkout0(clock_125M)       // output
// );
endmodule