module valve_data_parser (
    input  wire        clk,                // 系统时钟
    input  wire        rst_n,              // 异步复位，低电平有效
    
    // FIFO接口
    input  wire [7:0]  fifo_data,         // FIFO数据输入
    input  wire        fifo_empty,         // FIFO空标志
    output reg         fifo_rd_en,         // FIFO读使能
    
    input               udp_rx_done,        // udp接收完成信号（FIFO非空）

    // bit_to_64bit_converter接口
    input  wire        converter_ready,    // converter就绪信号
    input  wire        next_data_req,      // converter请求下一数据
    output reg         out_valid,          // 输出数据有效
    output reg [127:0] out_data,          // 128位输出数据
    
    // 解析出的包头信息
    output reg [31:0]  packet_type,       // 包类型
    output reg [31:0]  packet_size,       // 包大小
    output reg         device_start_stop, // 设备启动/停止
    output reg [31:0]  blow_delay,        // 喷吹延时
    output reg [31:0]  trigger_time,      // 行触发时间
    output reg [31:0]  total_packets,     // 数据总包数
    output reg         header_valid,        // 包头信息有效
    output reg [31:0]  pac_count,         //喷吹包计数
    output reg [31:0]  udp_pac_num,       //需要修改一下协议，发送的数据包中增加包号，用来判断是否丢包
    output reg [31:0]  fpga_trigger_time,  // FPGA触发时间
    output reg         fpga_trigger_method, // FPGA触发方式
    output reg [31:0]  fpga_pulse_width_us, // FPGA脉冲宽度(us)
    output reg         one_pac_done      // one_pac_done信号
);

// 状态定义
localparam IDLE         = 4'd0;  // 空闲状态
localparam PRE_READ     = 4'd1;  
localparam READ_HEADER  = 4'd2;  // 读取包头
localparam PARSE_HEADER = 4'd3;  // 解析包头
localparam WAIT_READY   = 4'd4;  // 等待converter就绪
localparam READ_DATA    = 4'd5;  // 读取喷吹数据
localparam SEND_DATA    = 4'd6;  // 发送数据到converter
localparam WAIT_REQ     = 4'd7;  // 等待next_data_req


// 计数器和寄存器
reg [3:0]   current_state;
reg [3:0]   next_state;
reg [5:0]   byte_count;          // 字节计数器
reg [3:0]   field_count;         // 字段计数器
reg [127:0] data_buffer;         // 数据缓存
reg [31:0]  header_buffer;       // 包头缓存

reg         next_data_req_r;     // next_data_req延迟
reg         converter_ready_r;   // converter_ready延迟
reg         udp_rx_done_r;
reg         udp_rx_done_r1;

reg [31:0]  data_bytes_sent;     // 已发送的数据字节数
reg [31:0]  valid_data_size;     // 有效数据长度(packet_size-32)

reg [31:0] data_temp,data_temp2; 

// 边沿检测
wire next_data_req_pos;          // next_data_req上升沿
wire converter_ready_pos;        // converter_ready上升沿
wire udp_rx_done_pos;

assign next_data_req_pos = next_data_req & !next_data_req_r;
assign converter_ready_pos = converter_ready & !converter_ready_r;
assign  udp_rx_done_pos = udp_rx_done_r & !udp_rx_done_r1;

// 边沿检测寄存器
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        next_data_req_r <= 1'b0;
        converter_ready_r <= 1'b0;
        udp_rx_done_r <= 1'b0;
        udp_rx_done_r1 <= 1'b0;
    end else begin
        next_data_req_r <= next_data_req;
        converter_ready_r <= converter_ready;
        udp_rx_done_r1 <= udp_rx_done_r;
        udp_rx_done_r <= udp_rx_done;
    end
end

// 状态机转换
always @(posedge clk or negedge rst_n) begin
    if (!rst_n)
        current_state <= IDLE;
    else
        current_state <= next_state;
end

// 状态转换逻辑
always @(*) begin
    next_state = current_state;
    case (current_state)
        IDLE: begin
            if (udp_rx_done_pos)
                next_state = PRE_READ;
        end
        
        PRE_READ: begin
            if (!fifo_empty)
                next_state = READ_HEADER;
        end

        READ_HEADER: begin
            if (byte_count == 6'd32) // 32字节包头读取完成
                next_state = PARSE_HEADER;
        end
        
        PARSE_HEADER: begin
            if(packet_type == 32'd0)//控制包种类，读完就回到idle，发送控制包时不能开采集
                next_state = IDLE;
            else
                next_state = WAIT_READY;//数据包就进入下一个状态，等待converter就绪
        end
        
        WAIT_READY: begin
            if ((converter_ready || next_data_req_pos ||next_data_req))
                next_state = READ_DATA;
            // if ((converter_ready || next_data_req_pos ||next_data_req) && data_bytes_sent < valid_data_size)
            //     next_state = READ_DATA;
            // else if (data_bytes_sent >= valid_data_size)
            //     next_state = IDLE;
        end
        
        READ_DATA: begin
            if (byte_count == 6'd16) // 16字节数据读取完成
                next_state = SEND_DATA;
        end
        
        SEND_DATA: begin
            // if (data_bytes_sent >= valid_data_size)
            //     next_state = IDLE;
            // else
                next_state = WAIT_REQ;
        end
        
        WAIT_REQ: begin
            if (data_bytes_sent >= valid_data_size && (!fifo_empty)) begin//如果不为空，直接读取下一帧
                next_state = PRE_READ;
            end
            else if (data_bytes_sent >= valid_data_size) begin
                next_state = IDLE;
            end
            else if (next_data_req_pos || converter_ready_pos) begin
                next_state = READ_DATA;
            end
        end
        
        default: next_state = IDLE;
    endcase
end

// 数据处理逻辑
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        byte_count <= 6'd0;
        field_count <= 4'd0;
        fifo_rd_en <= 1'b0;
        out_valid <= 1'b0;
        header_valid <= 1'b0;
        out_data <= 128'd0;
        header_buffer <= 32'd0;
        {packet_type, packet_size, blow_delay, trigger_time, total_packets} <= 160'd0;
        data_bytes_sent <= 32'd0;
        valid_data_size <= 32'd0;
        one_pac_done <= 1'b0;
        pac_count <= 32'd0;
        data_buffer <= 128'd0;
        udp_pac_num <= 32'd0;
        device_start_stop <= 1'b0;
        fpga_trigger_time <= 32'd0;
        fpga_trigger_method <= 1'b0;
        fpga_pulse_width_us <= 32'd20;  // 默认20us
    end else begin
        fifo_rd_en <= 1'b0;
        out_valid <= 1'b0;
        header_valid <= 1'b0;
        one_pac_done <= 1'b0;
        device_start_stop <= 1'b0;//检测上升沿
        case (current_state)
            IDLE: begin
                byte_count <= 6'd0;
                field_count <= 4'd0;
                data_bytes_sent <= 32'd0;
                if (!fifo_empty) begin
                    fifo_rd_en <= 1'b1;
                end
            end
            
            PRE_READ: begin
                if (!fifo_empty) begin
                    fifo_rd_en <= 1'b1;
                end
            end

            READ_HEADER: begin
                // if (!fifo_empty) begin
                    if (byte_count >= 6'd31) begin
                        fifo_rd_en <= 1'b0;
                    end else begin
                        fifo_rd_en <= 1'b1;
                    end
                    header_buffer <= {header_buffer[23:0], fifo_data};
                    byte_count <= byte_count + 1'b1;
                    
                    if (byte_count[1:0] == 2'b11) begin // 每4字节更新一个字段
                        case (field_count)
                            4'd0: packet_type   <= {header_buffer[23:0], fifo_data};
                            4'd1: packet_size   <= {header_buffer[23:0], fifo_data};
                            4'd2: blow_delay    <= {header_buffer[23:0], fifo_data};
                            4'd3: trigger_time  <= {header_buffer[23:0], fifo_data};
                            4'd4: total_packets <= {header_buffer[23:0], fifo_data};
                            4'd5: data_temp     <= {header_buffer[23:0], fifo_data};
                            4'd6: data_temp2    <= {header_buffer[23:0], fifo_data};
                        endcase
                        field_count <= field_count + 1'b1;
                    end
                // end
            end
            
            PARSE_HEADER: begin
                if (packet_type == 32'd0) begin
                    device_start_stop <= packet_size[0];
                    fpga_trigger_time <= data_temp;
                    fpga_trigger_method <= data_temp2[0];
                    fpga_pulse_width_us <= data_temp2[31:1]; // 使用data_temp2的高31位存储脉冲宽度
                end
                else begin
                    udp_pac_num <= data_temp;
                end
                header_valid <= 1'b1;
                byte_count <= 6'd0;
                valid_data_size <= packet_size - 32'd32; // 计算有效数据长度
            end
            
            READ_DATA: begin
                if (!fifo_empty) begin
                    if (byte_count >= 6'd16) begin
                        fifo_rd_en <= 1'b0;
                    end else begin
                        fifo_rd_en <= 1'b1;
                    end
                    data_buffer <= {data_buffer[119:0], fifo_data};
                    byte_count <= byte_count + 1'b1;
                end
            end
            
            SEND_DATA: begin
                out_valid <= 1'b1;
                out_data <= data_buffer;
                byte_count <= 6'd0;
                data_bytes_sent <= data_bytes_sent + 32'd16; // 更新已发送字节数
            end

            WAIT_REQ: begin
                if (data_bytes_sent >= valid_data_size && (packet_type == total_packets)) begin
                    one_pac_done <= 1'b1;
                    pac_count <= pac_count + 32'd1;
                end 
                if (data_bytes_sent >= valid_data_size && (!fifo_empty)) begin
                    data_bytes_sent <= 32'd0;
                    header_buffer <= 32'd0;
                    field_count <= 4'd0;
                    byte_count <= 6'd0;
                    // {packet_type, packet_size, blow_delay, trigger_time, total_packets} <= 160'd0;
                end
            end
            
            default: begin
                byte_count <= byte_count;
            end
        endcase
    end
end

endmodule
