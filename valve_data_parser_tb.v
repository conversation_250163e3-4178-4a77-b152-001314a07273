`timescale 1ns/1ps

module valve_data_parser_tb();

    // 时钟和复位信号
    reg         clk;
    reg         rst_n;
    
    // FIFO接口信号
    reg  [7:0]  fifo_data;
    reg         fifo_empty;
    wire        fifo_rd_en;
    
    reg udp_rx_done;
    // Converter接口信号
    reg         converter_ready;
    reg         next_data_req;
    wire        out_valid;
    wire [127:0] out_data;
    
    // 包头信息
    wire [31:0] packet_type;
    wire [31:0] packet_size;
    wire [31:0] blow_delay;
    wire [31:0] trigger_time;
    wire [31:0] total_packets;
    wire        header_valid;
    wire        one_pac_done;
    // 测试数据存储
    reg [7:0] test_data [0:1663]; // 存储一个完整的阀数据包 (32 + 1280 bytes + 32 + 16*20)
    integer data_index;

    // 实例化被测模块
    valve_data_parser u_valve_data_parser (
        .clk(clk),
        .rst_n(rst_n),
        .fifo_data(fifo_data),
        .fifo_empty(fifo_empty),
        .fifo_rd_en(fifo_rd_en),
        .udp_rx_done(udp_rx_done),
        .converter_ready(converter_ready),
        .next_data_req(next_data_req),
        .out_valid(out_valid),
        .out_data(out_data),
        .packet_type(packet_type),
        .packet_size(packet_size),
        .blow_delay(blow_delay),
        .trigger_time(trigger_time),
        .total_packets(total_packets),
        .header_valid(header_valid),
        .one_pac_done(one_pac_done)
    );

    // 时钟生成
    initial begin
        clk = 0;
        forever #5 clk = ~clk;
    end

    // 测试数据生成任务
    task init_test_data;
        integer i;
        begin
            // 包头数据 (32 bytes)
            // 包类型: 1 (第一包阀数据)
            test_data[0] = 8'h00; test_data[1] = 8'h00; 
            test_data[2] = 8'h00; test_data[3] = 8'h01;
            
            // 包大小: 1312 bytes
            test_data[4] = 8'h00; test_data[5] = 8'h00; 
            test_data[6] = 8'h05; test_data[7] = 8'h20;
            
            // 喷吹延时: 1000
            test_data[8] = 8'h00; test_data[9] = 8'h00;
            test_data[10] = 8'h03; test_data[11] = 8'hE8;
            
            // 行触发时间: 800
            test_data[12] = 8'h00; test_data[13] = 8'h00;
            test_data[14] = 8'h03; test_data[15] = 8'h20;
            
            // 数据总包数: 2
            test_data[16] = 8'h00; test_data[17] = 8'h00;
            test_data[18] = 8'h00; test_data[19] = 8'h02;
            
            // 空字段 (12 bytes)
            for(i = 20; i < 32; i = i + 1) begin
                test_data[i] = 8'h00;
            end
            
            // 喷吹指令数据 (1280 bytes)
            for(i = 32; i < 1312; i = i + 1) begin
                test_data[i] = i[7:0]; // 使用递增数据方便验证
            end

            // 包头数据 (32 bytes)
            // 包类型: 2 (第2包阀数据)
            test_data[1312] = 8'h00; test_data[1313] = 8'h00; 
            test_data[1314] = 8'h00; test_data[1315] = 8'h02;
            
            // 包大小: 352 bytes
            test_data[1316] = 8'h00; test_data[1317] = 8'h00; 
            test_data[1318] = 8'h01; test_data[1319] = 8'h60;
            
            // 喷吹延时: 2000
            test_data[1320] = 8'h00; test_data[1321] = 8'h00;
            test_data[1322] = 8'h07; test_data[1323] = 8'hd0;
            
            // 行触发时间: 800
            test_data[1324] = 8'h00; test_data[1325] = 8'h00;
            test_data[1326] = 8'h03; test_data[1327] = 8'h20;
            
            // 数据总包数: 2
            test_data[1328] = 8'h00; test_data[1329] = 8'h00;
            test_data[1330] = 8'h00; test_data[1331] = 8'h02;

            // 空字段 (12 bytes)
            for(i = 1332; i < 1344; i = i + 1) begin
                test_data[i] = 8'h00;
            end

            // 喷吹指令数据 (320 bytes)
            for(i = 1344; i < 1664; i = i + 1) begin
                test_data[i] = i[7:0]; // 使用递增数据方便验证
            end
        end
    endtask

    // 提供FIFO数据任务
    task provide_fifo_data;
        begin
            if (fifo_rd_en && !fifo_empty) begin
                fifo_data <= test_data[data_index];
                data_index <= data_index + 1;
                if (data_index >= 1664) begin
                    fifo_empty <= 1'b1;
                end
            end
        end
    endtask

    // 检查输出数据任务
    task check_output;
        begin
            if (out_valid) begin
                $display("Time=%0t: Output data = %h", $time, out_data);
            end
            if (header_valid) begin
                $display("Header Info:");
                $display("Packet Type: %h", packet_type);
                $display("Packet Size: %h", packet_size);
                $display("Blow Delay: %h", blow_delay);
                $display("Trigger Time: %h", trigger_time);
                $display("Total Packets: %h", total_packets);
            end
        end
    endtask

    // 测试激励
    initial begin
        // 初始化信号
        rst_n = 1'b1;
        fifo_empty = 1'b1;
        udp_rx_done = 1'b0;
        fifo_data = 8'haa;
        converter_ready = 1'b0;
        next_data_req = 1'b0;
        data_index = 0;
        
        // 初始化测试数据
        init_test_data();
        
        // 复位
        #100 rst_n = 1'b0;
        #100 rst_n = 1'b1;
        
        // 开始测试
        #100;
        udp_rx_done = 1'b1;
        fifo_empty = 1'b0;
        converter_ready = 1'b1;
        
        // 等待header_valid
        wait(header_valid);
        #100;
        
        // 模拟converter请求数据
        repeat(100) begin // 1312/16 = 82次数据传输
            #100;
            @(posedge clk);
            #1 next_data_req = 1'b1;
            // #10;
            @(posedge clk);
            #1 next_data_req = 1'b0;
            wait(out_valid);
            #20;
        end
        
        // 测试完成
        #1000;
        $display("Test completed!");
        $finish;
    end

    // 响应FIFO读取请求
    always @(posedge clk) begin
        provide_fifo_data();
    end

    // 监控输出
    always @(posedge clk) begin
        check_output();
    end

    // 生成波形文件
    initial begin
        $dumpfile("valve_data_parser_tb.vcd");
        $dumpvars(0, valve_data_parser_tb);
    end

endmodule
